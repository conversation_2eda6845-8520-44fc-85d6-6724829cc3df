import { useState, useRef } from 'react';
import useAuthStore from '../stores/authStore';

interface AudioFile {
  url: string;
  duration?: number;
}

interface UseAudioRecorderReturn {
  isRecording: boolean;
  recordingDuration: string;
  audioBlob: Blob | null;
  audioUrl: string | null;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  uploadAudio: () => Promise<AudioFile | null>;
  resetRecording: () => void;
}

const useAudioRecorder = (): UseAudioRecorderReturn => {
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordingDuration, setRecordingDuration] = useState<number>(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const intervalRef = useRef<number | null>(null);

  const { token } = useAuthStore();

  const startRecording = async (): Promise<void> => {
    try {
      console.log('Requesting microphone access...');

      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia is not supported in this browser');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
        }
      });

      console.log('Microphone access granted');
      streamRef.current = stream;
      chunksRef.current = [];

      // Check MediaRecorder support and choose best format
      let mimeType = 'audio/webm;codecs=opus';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = 'audio/webm';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/mp4';
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = ''; // Let browser choose
          }
        }
      }

      console.log('Using MIME type:', mimeType);

      const mediaRecorder = new MediaRecorder(stream, mimeType ? { mimeType } : undefined);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event: BlobEvent) => {
        console.log('Data available:', event.data.size, 'bytes');
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        console.log('Recording stopped, creating blob...');
        const blob = new Blob(chunksRef.current, { type: mimeType || 'audio/webm' });
        console.log('Blob created:', blob.size, 'bytes');
        setAudioBlob(blob);
        setAudioUrl(URL.createObjectURL(blob));

        // Stop all tracks
        streamRef.current?.getTracks().forEach(track => {
          track.stop();
          console.log('Track stopped:', track.kind);
        });
      };

      mediaRecorder.onerror = (event: any) => {
        console.error('MediaRecorder error:', event.error);
      };

      mediaRecorder.start(100); // Collect data every 100ms
      setIsRecording(true);
      setRecordingDuration(0);

      console.log('Recording started');

      // Start duration timer
      intervalRef.current = window.setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

    } catch (error: any) {
      console.error('Error starting recording:', error);

      let errorMessage = 'Could not access microphone. ';

      if (error.name === 'NotAllowedError') {
        errorMessage += 'Please allow microphone access and try again.';
      } else if (error.name === 'NotFoundError') {
        errorMessage += 'No microphone found. Please connect a microphone.';
      } else if (error.name === 'NotSupportedError') {
        errorMessage += 'Audio recording is not supported in this browser.';
      } else {
        errorMessage += error.message || 'Unknown error occurred.';
      }

      alert(errorMessage);
    }
  };

  const stopRecording = (): void => {
    console.log('Stopping recording...');
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      console.log('Recording stopped');
    }
  };

  const uploadAudio = async (): Promise<AudioFile | null> => {
    if (!audioBlob || !token) return null;

    const formData = new FormData();
    formData.append('audio', audioBlob, 'voice-note.webm');

    try {
      const response = await fetch('http://localhost:5000/api/upload/audio', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        return data.audioFile;
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error uploading audio:', error);
      return null;
    }
  };

  const resetRecording = (): void => {
    setAudioBlob(null);
    setAudioUrl(null);
    setRecordingDuration(0);
    chunksRef.current = [];
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return {
    isRecording,
    recordingDuration: formatDuration(recordingDuration),
    audioBlob,
    audioUrl,
    startRecording,
    stopRecording,
    uploadAudio,
    resetRecording,
  };
};

export default useAudioRecorder;
