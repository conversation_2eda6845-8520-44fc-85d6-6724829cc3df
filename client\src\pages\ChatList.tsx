import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import useAuthStore from '../stores/authStore';

interface Chat {
  _id: string;
  name?: string;
  participants: Array<{
    _id: string;
    username: string;
  }>;
  lastMessage?: {
    content?: string;
    messageType: string;
    createdAt: string;
  };
  updatedAt: string;
  isGroupChat: boolean;
}

const ChatList: React.FC = () => {
  const [chats, setChats] = useState<Chat[]>([]);
  const [showNewChatModal, setShowNewChatModal] = useState(false);
  const [newChatEmail, setNewChatEmail] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const { user, token, logout } = useAuthStore();

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/chats', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setChats(data);
      } else if (response.status === 401) {
        logout();
      }
    } catch (error) {
      console.error('Error loading chats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createNewChat = async () => {
    if (!newChatEmail.trim()) return;

    try {
      const response = await fetch('http://localhost:5000/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          participantEmail: newChatEmail,
        }),
      });

      if (response.ok) {
        const newChat = await response.json();
        setChats(prev => [newChat, ...prev]);
        setShowNewChatModal(false);
        setNewChatEmail('');
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to create chat');
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      alert('Failed to create chat');
    }
  };

  const getChatName = (chat: Chat) => {
    if (chat.isGroupChat) {
      return chat.name || 'Group Chat';
    }

    const otherParticipant = chat.participants.find(p => p._id !== user?.id);
    return otherParticipant?.username || 'Unknown User';
  };

  const getLastMessageText = (chat: Chat) => {
    if (!chat.lastMessage) return 'No messages yet';

    if (chat.lastMessage.messageType === 'voice') {
      return '🎵 Voice message';
    }

    return chat.lastMessage.content || 'Message';
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg">Loading chats...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Header */}
      <div className="bg-whatsapp-green text-white p-4 flex items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold">EchoGram</h1>
          <p className="text-sm opacity-90">Welcome, {user?.username}</p>
        </div>
        <button
          onClick={logout}
          className="text-white hover:text-gray-200 text-sm"
        >
          Logout
        </button>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {chats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <p className="text-lg font-medium">No chats yet</p>
            <p className="text-sm">Start a new conversation below</p>
          </div>
        ) : (
          chats.map((chat) => (
            <Link
              key={chat._id}
              to={`/chat/${chat._id}`}
              className="flex items-center p-4 hover:bg-gray-50 border-b border-gray-100"
            >
              <div className="w-12 h-12 bg-whatsapp-green rounded-full flex items-center justify-center mr-3">
                <span className="text-white font-medium text-lg">
                  {getChatName(chat).charAt(0).toUpperCase()}
                </span>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-text-primary font-medium truncate">
                    {getChatName(chat)}
                  </h3>
                  <span className="text-text-secondary text-xs">
                    {formatTime(chat.lastMessage?.createdAt || chat.updatedAt)}
                  </span>
                </div>

                <div className="flex items-center justify-between mt-1">
                  <p className="text-text-secondary text-sm truncate">
                    {getLastMessageText(chat)}
                  </p>
                </div>
              </div>
            </Link>
          ))
        )}
      </div>

      {/* New Chat Button */}
      <div className="p-4">
        <button
          onClick={() => setShowNewChatModal(true)}
          className="w-full bg-whatsapp-green text-white py-3 rounded-lg font-medium hover:bg-whatsapp-green-light transition-colors"
        >
          Start New Chat
        </button>
      </div>

      {/* New Chat Modal */}
      {showNewChatModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-80 mx-4">
            <h3 className="text-lg font-semibold mb-4">Start New Chat</h3>
            <input
              type="email"
              value={newChatEmail}
              onChange={(e) => setNewChatEmail(e.target.value)}
              placeholder="Enter user's email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-whatsapp-green mb-4"
            />
            <div className="flex space-x-3">
              <button
                onClick={createNewChat}
                className="flex-1 bg-whatsapp-green text-white py-2 rounded-md hover:bg-whatsapp-green-light"
              >
                Create Chat
              </button>
              <button
                onClick={() => {
                  setShowNewChatModal(false);
                  setNewChatEmail('');
                }}
                className="flex-1 bg-gray-500 text-white py-2 rounded-md hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatList;
