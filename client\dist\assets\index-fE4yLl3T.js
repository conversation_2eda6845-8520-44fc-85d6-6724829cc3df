(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const d of o)if(d.type==="childList")for(const b of d.addedNodes)b.tagName==="LINK"&&b.rel==="modulepreload"&&s(b)}).observe(document,{childList:!0,subtree:!0});function c(o){const d={};return o.integrity&&(d.integrity=o.integrity),o.referrerPolicy&&(d.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?d.credentials="include":o.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function s(o){if(o.ep)return;o.ep=!0;const d=c(o);fetch(o.href,d)}})();function Np(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Uc={exports:{}},Ua={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rd;function Mp(){if(rd)return Ua;rd=1;var r=Symbol.for("react.transitional.element"),u=Symbol.for("react.fragment");function c(s,o,d){var b=null;if(d!==void 0&&(b=""+d),o.key!==void 0&&(b=""+o.key),"key"in o){d={};for(var x in o)x!=="key"&&(d[x]=o[x])}else d=o;return o=d.ref,{$$typeof:r,type:s,key:b,ref:o!==void 0?o:null,props:d}}return Ua.Fragment=u,Ua.jsx=c,Ua.jsxs=c,Ua}var cd;function Dp(){return cd||(cd=1,Uc.exports=Mp()),Uc.exports}var q=Dp(),Bc={exports:{}},nt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sd;function wp(){if(sd)return nt;sd=1;var r=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),b=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),E=Symbol.for("react.lazy"),C=Symbol.iterator;function D(v){return v===null||typeof v!="object"?null:(v=C&&v[C]||v["@@iterator"],typeof v=="function"?v:null)}var Y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,L={};function V(v,H,G){this.props=v,this.context=H,this.refs=L,this.updater=G||Y}V.prototype.isReactComponent={},V.prototype.setState=function(v,H){if(typeof v!="object"&&typeof v!="function"&&v!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,v,H,"setState")},V.prototype.forceUpdate=function(v){this.updater.enqueueForceUpdate(this,v,"forceUpdate")};function B(){}B.prototype=V.prototype;function k(v,H,G){this.props=v,this.context=H,this.refs=L,this.updater=G||Y}var Q=k.prototype=new B;Q.constructor=k,U(Q,V.prototype),Q.isPureReactComponent=!0;var P=Array.isArray,J={H:null,A:null,T:null,S:null,V:null},_t=Object.prototype.hasOwnProperty;function Tt(v,H,G,j,$,ft){return G=ft.ref,{$$typeof:r,type:v,key:H,ref:G!==void 0?G:null,props:ft}}function et(v,H){return Tt(v.type,H,void 0,void 0,void 0,v.props)}function dt(v){return typeof v=="object"&&v!==null&&v.$$typeof===r}function $t(v){var H={"=":"=0",":":"=2"};return"$"+v.replace(/[=:]/g,function(G){return H[G]})}var he=/\/+/g;function Qt(v,H){return typeof v=="object"&&v!==null&&v.key!=null?$t(""+v.key):H.toString(36)}function Ol(){}function Nl(v){switch(v.status){case"fulfilled":return v.value;case"rejected":throw v.reason;default:switch(typeof v.status=="string"?v.then(Ol,Ol):(v.status="pending",v.then(function(H){v.status==="pending"&&(v.status="fulfilled",v.value=H)},function(H){v.status==="pending"&&(v.status="rejected",v.reason=H)})),v.status){case"fulfilled":return v.value;case"rejected":throw v.reason}}throw v}function Zt(v,H,G,j,$){var ft=typeof v;(ft==="undefined"||ft==="boolean")&&(v=null);var lt=!1;if(v===null)lt=!0;else switch(ft){case"bigint":case"string":case"number":lt=!0;break;case"object":switch(v.$$typeof){case r:case u:lt=!0;break;case E:return lt=v._init,Zt(lt(v._payload),H,G,j,$)}}if(lt)return $=$(v),lt=j===""?"."+Qt(v,0):j,P($)?(G="",lt!=null&&(G=lt.replace(he,"$&/")+"/"),Zt($,H,G,"",function(el){return el})):$!=null&&(dt($)&&($=et($,G+($.key==null||v&&v.key===$.key?"":(""+$.key).replace(he,"$&/")+"/")+lt)),H.push($)),1;lt=0;var le=j===""?".":j+":";if(P(v))for(var xt=0;xt<v.length;xt++)j=v[xt],ft=le+Qt(j,xt),lt+=Zt(j,H,G,ft,$);else if(xt=D(v),typeof xt=="function")for(v=xt.call(v),xt=0;!(j=v.next()).done;)j=j.value,ft=le+Qt(j,xt++),lt+=Zt(j,H,G,ft,$);else if(ft==="object"){if(typeof v.then=="function")return Zt(Nl(v),H,G,j,$);throw H=String(v),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(v).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.")}return lt}function M(v,H,G){if(v==null)return v;var j=[],$=0;return Zt(v,j,"","",function(ft){return H.call(G,ft,$++)}),j}function X(v){if(v._status===-1){var H=v._result;H=H(),H.then(function(G){(v._status===0||v._status===-1)&&(v._status=1,v._result=G)},function(G){(v._status===0||v._status===-1)&&(v._status=2,v._result=G)}),v._status===-1&&(v._status=0,v._result=H)}if(v._status===1)return v._result.default;throw v._result}var I=typeof reportError=="function"?reportError:function(v){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var H=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof v=="object"&&v!==null&&typeof v.message=="string"?String(v.message):String(v),error:v});if(!window.dispatchEvent(H))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",v);return}console.error(v)};function bt(){}return nt.Children={map:M,forEach:function(v,H,G){M(v,function(){H.apply(this,arguments)},G)},count:function(v){var H=0;return M(v,function(){H++}),H},toArray:function(v){return M(v,function(H){return H})||[]},only:function(v){if(!dt(v))throw Error("React.Children.only expected to receive a single React element child.");return v}},nt.Component=V,nt.Fragment=c,nt.Profiler=o,nt.PureComponent=k,nt.StrictMode=s,nt.Suspense=p,nt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=J,nt.__COMPILER_RUNTIME={__proto__:null,c:function(v){return J.H.useMemoCache(v)}},nt.cache=function(v){return function(){return v.apply(null,arguments)}},nt.cloneElement=function(v,H,G){if(v==null)throw Error("The argument must be a React element, but you passed "+v+".");var j=U({},v.props),$=v.key,ft=void 0;if(H!=null)for(lt in H.ref!==void 0&&(ft=void 0),H.key!==void 0&&($=""+H.key),H)!_t.call(H,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&H.ref===void 0||(j[lt]=H[lt]);var lt=arguments.length-2;if(lt===1)j.children=G;else if(1<lt){for(var le=Array(lt),xt=0;xt<lt;xt++)le[xt]=arguments[xt+2];j.children=le}return Tt(v.type,$,void 0,void 0,ft,j)},nt.createContext=function(v){return v={$$typeof:b,_currentValue:v,_currentValue2:v,_threadCount:0,Provider:null,Consumer:null},v.Provider=v,v.Consumer={$$typeof:d,_context:v},v},nt.createElement=function(v,H,G){var j,$={},ft=null;if(H!=null)for(j in H.key!==void 0&&(ft=""+H.key),H)_t.call(H,j)&&j!=="key"&&j!=="__self"&&j!=="__source"&&($[j]=H[j]);var lt=arguments.length-2;if(lt===1)$.children=G;else if(1<lt){for(var le=Array(lt),xt=0;xt<lt;xt++)le[xt]=arguments[xt+2];$.children=le}if(v&&v.defaultProps)for(j in lt=v.defaultProps,lt)$[j]===void 0&&($[j]=lt[j]);return Tt(v,ft,void 0,void 0,null,$)},nt.createRef=function(){return{current:null}},nt.forwardRef=function(v){return{$$typeof:x,render:v}},nt.isValidElement=dt,nt.lazy=function(v){return{$$typeof:E,_payload:{_status:-1,_result:v},_init:X}},nt.memo=function(v,H){return{$$typeof:m,type:v,compare:H===void 0?null:H}},nt.startTransition=function(v){var H=J.T,G={};J.T=G;try{var j=v(),$=J.S;$!==null&&$(G,j),typeof j=="object"&&j!==null&&typeof j.then=="function"&&j.then(bt,I)}catch(ft){I(ft)}finally{J.T=H}},nt.unstable_useCacheRefresh=function(){return J.H.useCacheRefresh()},nt.use=function(v){return J.H.use(v)},nt.useActionState=function(v,H,G){return J.H.useActionState(v,H,G)},nt.useCallback=function(v,H){return J.H.useCallback(v,H)},nt.useContext=function(v){return J.H.useContext(v)},nt.useDebugValue=function(){},nt.useDeferredValue=function(v,H){return J.H.useDeferredValue(v,H)},nt.useEffect=function(v,H,G){var j=J.H;if(typeof G=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return j.useEffect(v,H)},nt.useId=function(){return J.H.useId()},nt.useImperativeHandle=function(v,H,G){return J.H.useImperativeHandle(v,H,G)},nt.useInsertionEffect=function(v,H){return J.H.useInsertionEffect(v,H)},nt.useLayoutEffect=function(v,H){return J.H.useLayoutEffect(v,H)},nt.useMemo=function(v,H){return J.H.useMemo(v,H)},nt.useOptimistic=function(v,H){return J.H.useOptimistic(v,H)},nt.useReducer=function(v,H,G){return J.H.useReducer(v,H,G)},nt.useRef=function(v){return J.H.useRef(v)},nt.useState=function(v){return J.H.useState(v)},nt.useSyncExternalStore=function(v,H,G){return J.H.useSyncExternalStore(v,H,G)},nt.useTransition=function(){return J.H.useTransition()},nt.version="19.1.0",nt}var fd;function ts(){return fd||(fd=1,Bc.exports=wp()),Bc.exports}var O=ts();const od=Np(O);var Hc={exports:{}},Ba={},qc={exports:{}},Lc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hd;function zp(){return hd||(hd=1,function(r){function u(M,X){var I=M.length;M.push(X);t:for(;0<I;){var bt=I-1>>>1,v=M[bt];if(0<o(v,X))M[bt]=X,M[I]=v,I=bt;else break t}}function c(M){return M.length===0?null:M[0]}function s(M){if(M.length===0)return null;var X=M[0],I=M.pop();if(I!==X){M[0]=I;t:for(var bt=0,v=M.length,H=v>>>1;bt<H;){var G=2*(bt+1)-1,j=M[G],$=G+1,ft=M[$];if(0>o(j,I))$<v&&0>o(ft,j)?(M[bt]=ft,M[$]=I,bt=$):(M[bt]=j,M[G]=I,bt=G);else if($<v&&0>o(ft,I))M[bt]=ft,M[$]=I,bt=$;else break t}}return X}function o(M,X){var I=M.sortIndex-X.sortIndex;return I!==0?I:M.id-X.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;r.unstable_now=function(){return d.now()}}else{var b=Date,x=b.now();r.unstable_now=function(){return b.now()-x}}var p=[],m=[],E=1,C=null,D=3,Y=!1,U=!1,L=!1,V=!1,B=typeof setTimeout=="function"?setTimeout:null,k=typeof clearTimeout=="function"?clearTimeout:null,Q=typeof setImmediate<"u"?setImmediate:null;function P(M){for(var X=c(m);X!==null;){if(X.callback===null)s(m);else if(X.startTime<=M)s(m),X.sortIndex=X.expirationTime,u(p,X);else break;X=c(m)}}function J(M){if(L=!1,P(M),!U)if(c(p)!==null)U=!0,_t||(_t=!0,Qt());else{var X=c(m);X!==null&&Zt(J,X.startTime-M)}}var _t=!1,Tt=-1,et=5,dt=-1;function $t(){return V?!0:!(r.unstable_now()-dt<et)}function he(){if(V=!1,_t){var M=r.unstable_now();dt=M;var X=!0;try{t:{U=!1,L&&(L=!1,k(Tt),Tt=-1),Y=!0;var I=D;try{e:{for(P(M),C=c(p);C!==null&&!(C.expirationTime>M&&$t());){var bt=C.callback;if(typeof bt=="function"){C.callback=null,D=C.priorityLevel;var v=bt(C.expirationTime<=M);if(M=r.unstable_now(),typeof v=="function"){C.callback=v,P(M),X=!0;break e}C===c(p)&&s(p),P(M)}else s(p);C=c(p)}if(C!==null)X=!0;else{var H=c(m);H!==null&&Zt(J,H.startTime-M),X=!1}}break t}finally{C=null,D=I,Y=!1}X=void 0}}finally{X?Qt():_t=!1}}}var Qt;if(typeof Q=="function")Qt=function(){Q(he)};else if(typeof MessageChannel<"u"){var Ol=new MessageChannel,Nl=Ol.port2;Ol.port1.onmessage=he,Qt=function(){Nl.postMessage(null)}}else Qt=function(){B(he,0)};function Zt(M,X){Tt=B(function(){M(r.unstable_now())},X)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(M){M.callback=null},r.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):et=0<M?Math.floor(1e3/M):5},r.unstable_getCurrentPriorityLevel=function(){return D},r.unstable_next=function(M){switch(D){case 1:case 2:case 3:var X=3;break;default:X=D}var I=D;D=X;try{return M()}finally{D=I}},r.unstable_requestPaint=function(){V=!0},r.unstable_runWithPriority=function(M,X){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var I=D;D=M;try{return X()}finally{D=I}},r.unstable_scheduleCallback=function(M,X,I){var bt=r.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?bt+I:bt):I=bt,M){case 1:var v=-1;break;case 2:v=250;break;case 5:v=1073741823;break;case 4:v=1e4;break;default:v=5e3}return v=I+v,M={id:E++,callback:X,priorityLevel:M,startTime:I,expirationTime:v,sortIndex:-1},I>bt?(M.sortIndex=I,u(m,M),c(p)===null&&M===c(m)&&(L?(k(Tt),Tt=-1):L=!0,Zt(J,I-bt))):(M.sortIndex=v,u(p,M),U||Y||(U=!0,_t||(_t=!0,Qt()))),M},r.unstable_shouldYield=$t,r.unstable_wrapCallback=function(M){var X=D;return function(){var I=D;D=X;try{return M.apply(this,arguments)}finally{D=I}}}}(Lc)),Lc}var dd;function Cp(){return dd||(dd=1,qc.exports=zp()),qc.exports}var jc={exports:{}},Jt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var md;function Up(){if(md)return Jt;md=1;var r=ts();function u(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var E=2;E<arguments.length;E++)m+="&args[]="+encodeURIComponent(arguments[E])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var s={d:{f:c,r:function(){throw Error(u(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},o=Symbol.for("react.portal");function d(p,m,E){var C=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:C==null?null:""+C,children:p,containerInfo:m,implementation:E}}var b=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function x(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return Jt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Jt.createPortal=function(p,m){var E=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(u(299));return d(p,m,null,E)},Jt.flushSync=function(p){var m=b.T,E=s.p;try{if(b.T=null,s.p=2,p)return p()}finally{b.T=m,s.p=E,s.d.f()}},Jt.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,s.d.C(p,m))},Jt.prefetchDNS=function(p){typeof p=="string"&&s.d.D(p)},Jt.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var E=m.as,C=x(E,m.crossOrigin),D=typeof m.integrity=="string"?m.integrity:void 0,Y=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;E==="style"?s.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:C,integrity:D,fetchPriority:Y}):E==="script"&&s.d.X(p,{crossOrigin:C,integrity:D,fetchPriority:Y,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},Jt.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var E=x(m.as,m.crossOrigin);s.d.M(p,{crossOrigin:E,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&s.d.M(p)},Jt.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var E=m.as,C=x(E,m.crossOrigin);s.d.L(p,E,{crossOrigin:C,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},Jt.preloadModule=function(p,m){if(typeof p=="string")if(m){var E=x(m.as,m.crossOrigin);s.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:E,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else s.d.m(p)},Jt.requestFormReset=function(p){s.d.r(p)},Jt.unstable_batchedUpdates=function(p,m){return p(m)},Jt.useFormState=function(p,m,E){return b.H.useFormState(p,m,E)},Jt.useFormStatus=function(){return b.H.useHostTransitionStatus()},Jt.version="19.1.0",Jt}var yd;function Bp(){if(yd)return jc.exports;yd=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(u){console.error(u)}}return r(),jc.exports=Up(),jc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pd;function Hp(){if(pd)return Ba;pd=1;var r=Cp(),u=ts(),c=Bp();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function b(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function x(t){if(d(t)!==t)throw Error(s(188))}function p(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(s(188));return e!==t?null:t}for(var l=t,n=e;;){var a=l.return;if(a===null)break;var i=a.alternate;if(i===null){if(n=a.return,n!==null){l=n;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===l)return x(a),t;if(i===n)return x(a),e;i=i.sibling}throw Error(s(188))}if(l.return!==n.return)l=a,n=i;else{for(var f=!1,h=a.child;h;){if(h===l){f=!0,l=a,n=i;break}if(h===n){f=!0,n=a,l=i;break}h=h.sibling}if(!f){for(h=i.child;h;){if(h===l){f=!0,l=i,n=a;break}if(h===n){f=!0,n=i,l=a;break}h=h.sibling}if(!f)throw Error(s(189))}}if(l.alternate!==n)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var E=Object.assign,C=Symbol.for("react.element"),D=Symbol.for("react.transitional.element"),Y=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),V=Symbol.for("react.profiler"),B=Symbol.for("react.provider"),k=Symbol.for("react.consumer"),Q=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),J=Symbol.for("react.suspense"),_t=Symbol.for("react.suspense_list"),Tt=Symbol.for("react.memo"),et=Symbol.for("react.lazy"),dt=Symbol.for("react.activity"),$t=Symbol.for("react.memo_cache_sentinel"),he=Symbol.iterator;function Qt(t){return t===null||typeof t!="object"?null:(t=he&&t[he]||t["@@iterator"],typeof t=="function"?t:null)}var Ol=Symbol.for("react.client.reference");function Nl(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Ol?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case U:return"Fragment";case V:return"Profiler";case L:return"StrictMode";case J:return"Suspense";case _t:return"SuspenseList";case dt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case Y:return"Portal";case Q:return(t.displayName||"Context")+".Provider";case k:return(t._context.displayName||"Context")+".Consumer";case P:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Tt:return e=t.displayName||null,e!==null?e:Nl(t.type)||"Memo";case et:e=t._payload,t=t._init;try{return Nl(t(e))}catch{}}return null}var Zt=Array.isArray,M=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I={pending:!1,data:null,method:null,action:null},bt=[],v=-1;function H(t){return{current:t}}function G(t){0>v||(t.current=bt[v],bt[v]=null,v--)}function j(t,e){v++,bt[v]=t.current,t.current=e}var $=H(null),ft=H(null),lt=H(null),le=H(null);function xt(t,e){switch(j(lt,e),j(ft,t),j($,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Bh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Bh(e),t=Hh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}G($),j($,t)}function el(){G($),G(ft),G(lt)}function bi(t){t.memoizedState!==null&&j(le,t);var e=$.current,l=Hh(e,t.type);e!==l&&(j(ft,t),j($,l))}function Qa(t){ft.current===t&&(G($),G(ft)),le.current===t&&(G(le),Ma._currentValue=I)}var Si=Object.prototype.hasOwnProperty,Ei=r.unstable_scheduleCallback,_i=r.unstable_cancelCallback,im=r.unstable_shouldYield,rm=r.unstable_requestPaint,Oe=r.unstable_now,cm=r.unstable_getCurrentPriorityLevel,ds=r.unstable_ImmediatePriority,ms=r.unstable_UserBlockingPriority,Za=r.unstable_NormalPriority,sm=r.unstable_LowPriority,ys=r.unstable_IdlePriority,fm=r.log,om=r.unstable_setDisableYieldValue,qn=null,ne=null;function ll(t){if(typeof fm=="function"&&om(t),ne&&typeof ne.setStrictMode=="function")try{ne.setStrictMode(qn,t)}catch{}}var ae=Math.clz32?Math.clz32:mm,hm=Math.log,dm=Math.LN2;function mm(t){return t>>>=0,t===0?32:31-(hm(t)/dm|0)|0}var ka=256,Ka=4194304;function Ml(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ja(t,e,l){var n=t.pendingLanes;if(n===0)return 0;var a=0,i=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var h=n&134217727;return h!==0?(n=h&~i,n!==0?a=Ml(n):(f&=h,f!==0?a=Ml(f):l||(l=h&~t,l!==0&&(a=Ml(l))))):(h=n&~i,h!==0?a=Ml(h):f!==0?a=Ml(f):l||(l=n&~t,l!==0&&(a=Ml(l)))),a===0?0:e!==0&&e!==a&&(e&i)===0&&(i=a&-a,l=e&-e,i>=l||i===32&&(l&4194048)!==0)?e:a}function Ln(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function ym(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ps(){var t=ka;return ka<<=1,(ka&4194048)===0&&(ka=256),t}function vs(){var t=Ka;return Ka<<=1,(Ka&62914560)===0&&(Ka=4194304),t}function Ti(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function jn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function pm(t,e,l,n,a,i){var f=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var h=t.entanglements,y=t.expirationTimes,T=t.hiddenUpdates;for(l=f&~l;0<l;){var N=31-ae(l),z=1<<N;h[N]=0,y[N]=-1;var A=T[N];if(A!==null)for(T[N]=null,N=0;N<A.length;N++){var R=A[N];R!==null&&(R.lane&=-536870913)}l&=~z}n!==0&&gs(t,n,0),i!==0&&a===0&&t.tag!==0&&(t.suspendedLanes|=i&~(f&~e))}function gs(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var n=31-ae(e);t.entangledLanes|=e,t.entanglements[n]=t.entanglements[n]|1073741824|l&4194090}function bs(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var n=31-ae(l),a=1<<n;a&e|t[n]&e&&(t[n]|=e),l&=~a}}function xi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Ai(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Ss(){var t=X.p;return t!==0?t:(t=window.event,t===void 0?32:ed(t.type))}function vm(t,e){var l=X.p;try{return X.p=t,e()}finally{X.p=l}}var nl=Math.random().toString(36).slice(2),kt="__reactFiber$"+nl,Ft="__reactProps$"+nl,Jl="__reactContainer$"+nl,Ri="__reactEvents$"+nl,gm="__reactListeners$"+nl,bm="__reactHandles$"+nl,Es="__reactResources$"+nl,Yn="__reactMarker$"+nl;function Oi(t){delete t[kt],delete t[Ft],delete t[Ri],delete t[gm],delete t[bm]}function $l(t){var e=t[kt];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Jl]||l[kt]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=Yh(t);t!==null;){if(l=t[kt])return l;t=Yh(t)}return e}t=l,l=t.parentNode}return null}function Wl(t){if(t=t[kt]||t[Jl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Xn(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function Fl(t){var e=t[Es];return e||(e=t[Es]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function qt(t){t[Yn]=!0}var _s=new Set,Ts={};function Dl(t,e){Pl(t,e),Pl(t+"Capture",e)}function Pl(t,e){for(Ts[t]=e,t=0;t<e.length;t++)_s.add(e[t])}var Sm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),xs={},As={};function Em(t){return Si.call(As,t)?!0:Si.call(xs,t)?!1:Sm.test(t)?As[t]=!0:(xs[t]=!0,!1)}function $a(t,e,l){if(Em(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var n=e.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Wa(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Le(t,e,l,n){if(n===null)t.removeAttribute(l);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+n)}}var Ni,Rs;function Il(t){if(Ni===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);Ni=e&&e[1]||"",Rs=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ni+t+Rs}var Mi=!1;function Di(t,e){if(!t||Mi)return"";Mi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(e){var z=function(){throw Error()};if(Object.defineProperty(z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(z,[])}catch(R){var A=R}Reflect.construct(t,[],z)}else{try{z.call()}catch(R){A=R}t.call(z.prototype)}}else{try{throw Error()}catch(R){A=R}(z=t())&&typeof z.catch=="function"&&z.catch(function(){})}}catch(R){if(R&&A&&typeof R.stack=="string")return[R.stack,A.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=n.DetermineComponentFrameRoot(),f=i[0],h=i[1];if(f&&h){var y=f.split(`
`),T=h.split(`
`);for(a=n=0;n<y.length&&!y[n].includes("DetermineComponentFrameRoot");)n++;for(;a<T.length&&!T[a].includes("DetermineComponentFrameRoot");)a++;if(n===y.length||a===T.length)for(n=y.length-1,a=T.length-1;1<=n&&0<=a&&y[n]!==T[a];)a--;for(;1<=n&&0<=a;n--,a--)if(y[n]!==T[a]){if(n!==1||a!==1)do if(n--,a--,0>a||y[n]!==T[a]){var N=`
`+y[n].replace(" at new "," at ");return t.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",t.displayName)),N}while(1<=n&&0<=a);break}}}finally{Mi=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?Il(l):""}function _m(t){switch(t.tag){case 26:case 27:case 5:return Il(t.type);case 16:return Il("Lazy");case 13:return Il("Suspense");case 19:return Il("SuspenseList");case 0:case 15:return Di(t.type,!1);case 11:return Di(t.type.render,!1);case 1:return Di(t.type,!0);case 31:return Il("Activity");default:return""}}function Os(t){try{var e="";do e+=_m(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function de(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ns(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Tm(t){var e=Ns(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),n=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var a=l.get,i=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return a.call(this)},set:function(f){n=""+f,i.call(this,f)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return n},setValue:function(f){n=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Fa(t){t._valueTracker||(t._valueTracker=Tm(t))}function Ms(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),n="";return t&&(n=Ns(t)?t.checked?"true":"false":t.value),t=n,t!==l?(e.setValue(t),!0):!1}function Pa(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var xm=/[\n"\\]/g;function me(t){return t.replace(xm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function wi(t,e,l,n,a,i,f,h){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+de(e)):t.value!==""+de(e)&&(t.value=""+de(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?zi(t,f,de(e)):l!=null?zi(t,f,de(l)):n!=null&&t.removeAttribute("value"),a==null&&i!=null&&(t.defaultChecked=!!i),a!=null&&(t.checked=a&&typeof a!="function"&&typeof a!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.name=""+de(h):t.removeAttribute("name")}function Ds(t,e,l,n,a,i,f,h){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;l=l!=null?""+de(l):"",e=e!=null?""+de(e):l,h||e===t.value||(t.value=e),t.defaultValue=e}n=n??a,n=typeof n!="function"&&typeof n!="symbol"&&!!n,t.checked=h?t.checked:!!n,t.defaultChecked=!!n,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function zi(t,e,l){e==="number"&&Pa(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function tn(t,e,l,n){if(t=t.options,e){e={};for(var a=0;a<l.length;a++)e["$"+l[a]]=!0;for(l=0;l<t.length;l++)a=e.hasOwnProperty("$"+t[l].value),t[l].selected!==a&&(t[l].selected=a),a&&n&&(t[l].defaultSelected=!0)}else{for(l=""+de(l),e=null,a=0;a<t.length;a++){if(t[a].value===l){t[a].selected=!0,n&&(t[a].defaultSelected=!0);return}e!==null||t[a].disabled||(e=t[a])}e!==null&&(e.selected=!0)}}function ws(t,e,l){if(e!=null&&(e=""+de(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+de(l):""}function zs(t,e,l,n){if(e==null){if(n!=null){if(l!=null)throw Error(s(92));if(Zt(n)){if(1<n.length)throw Error(s(93));n=n[0]}l=n}l==null&&(l=""),e=l}l=de(e),t.defaultValue=l,n=t.textContent,n===l&&n!==""&&n!==null&&(t.value=n)}function en(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var Am=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Cs(t,e,l){var n=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?n?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":n?t.setProperty(e,l):typeof l!="number"||l===0||Am.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function Us(t,e,l){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,l!=null){for(var n in l)!l.hasOwnProperty(n)||e!=null&&e.hasOwnProperty(n)||(n.indexOf("--")===0?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="");for(var a in e)n=e[a],e.hasOwnProperty(a)&&l[a]!==n&&Cs(t,a,n)}else for(var i in e)e.hasOwnProperty(i)&&Cs(t,i,e[i])}function Ci(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Om=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ia(t){return Om.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ui=null;function Bi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ln=null,nn=null;function Bs(t){var e=Wl(t);if(e&&(t=e.stateNode)){var l=t[Ft]||null;t:switch(t=e.stateNode,e.type){case"input":if(wi(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+me(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var n=l[e];if(n!==t&&n.form===t.form){var a=n[Ft]||null;if(!a)throw Error(s(90));wi(n,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(e=0;e<l.length;e++)n=l[e],n.form===t.form&&Ms(n)}break t;case"textarea":ws(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&tn(t,!!l.multiple,e,!1)}}}var Hi=!1;function Hs(t,e,l){if(Hi)return t(e,l);Hi=!0;try{var n=t(e);return n}finally{if(Hi=!1,(ln!==null||nn!==null)&&(Lu(),ln&&(e=ln,t=nn,nn=ln=null,Bs(e),t)))for(e=0;e<t.length;e++)Bs(t[e])}}function Vn(t,e){var l=t.stateNode;if(l===null)return null;var n=l[Ft]||null;if(n===null)return null;l=n[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(t=t.type,n=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!n;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(s(231,e,typeof l));return l}var je=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),qi=!1;if(je)try{var Gn={};Object.defineProperty(Gn,"passive",{get:function(){qi=!0}}),window.addEventListener("test",Gn,Gn),window.removeEventListener("test",Gn,Gn)}catch{qi=!1}var al=null,Li=null,tu=null;function qs(){if(tu)return tu;var t,e=Li,l=e.length,n,a="value"in al?al.value:al.textContent,i=a.length;for(t=0;t<l&&e[t]===a[t];t++);var f=l-t;for(n=1;n<=f&&e[l-n]===a[i-n];n++);return tu=a.slice(t,1<n?1-n:void 0)}function eu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function lu(){return!0}function Ls(){return!1}function Pt(t){function e(l,n,a,i,f){this._reactName=l,this._targetInst=a,this.type=n,this.nativeEvent=i,this.target=f,this.currentTarget=null;for(var h in t)t.hasOwnProperty(h)&&(l=t[h],this[h]=l?l(i):i[h]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?lu:Ls,this.isPropagationStopped=Ls,this}return E(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=lu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=lu)},persist:function(){},isPersistent:lu}),e}var wl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},nu=Pt(wl),Qn=E({},wl,{view:0,detail:0}),Nm=Pt(Qn),ji,Yi,Zn,au=E({},Qn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Vi,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Zn&&(Zn&&t.type==="mousemove"?(ji=t.screenX-Zn.screenX,Yi=t.screenY-Zn.screenY):Yi=ji=0,Zn=t),ji)},movementY:function(t){return"movementY"in t?t.movementY:Yi}}),js=Pt(au),Mm=E({},au,{dataTransfer:0}),Dm=Pt(Mm),wm=E({},Qn,{relatedTarget:0}),Xi=Pt(wm),zm=E({},wl,{animationName:0,elapsedTime:0,pseudoElement:0}),Cm=Pt(zm),Um=E({},wl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Bm=Pt(Um),Hm=E({},wl,{data:0}),Ys=Pt(Hm),qm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Lm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},jm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ym(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=jm[t])?!!e[t]:!1}function Vi(){return Ym}var Xm=E({},Qn,{key:function(t){if(t.key){var e=qm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=eu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Lm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Vi,charCode:function(t){return t.type==="keypress"?eu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?eu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Vm=Pt(Xm),Gm=E({},au,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Xs=Pt(Gm),Qm=E({},Qn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Vi}),Zm=Pt(Qm),km=E({},wl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Km=Pt(km),Jm=E({},au,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),$m=Pt(Jm),Wm=E({},wl,{newState:0,oldState:0}),Fm=Pt(Wm),Pm=[9,13,27,32],Gi=je&&"CompositionEvent"in window,kn=null;je&&"documentMode"in document&&(kn=document.documentMode);var Im=je&&"TextEvent"in window&&!kn,Vs=je&&(!Gi||kn&&8<kn&&11>=kn),Gs=" ",Qs=!1;function Zs(t,e){switch(t){case"keyup":return Pm.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ks(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var an=!1;function ty(t,e){switch(t){case"compositionend":return ks(e);case"keypress":return e.which!==32?null:(Qs=!0,Gs);case"textInput":return t=e.data,t===Gs&&Qs?null:t;default:return null}}function ey(t,e){if(an)return t==="compositionend"||!Gi&&Zs(t,e)?(t=qs(),tu=Li=al=null,an=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Vs&&e.locale!=="ko"?null:e.data;default:return null}}var ly={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ks(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!ly[t.type]:e==="textarea"}function Js(t,e,l,n){ln?nn?nn.push(n):nn=[n]:ln=n,e=Qu(e,"onChange"),0<e.length&&(l=new nu("onChange","change",null,l,n),t.push({event:l,listeners:e}))}var Kn=null,Jn=null;function ny(t){Dh(t,0)}function uu(t){var e=Xn(t);if(Ms(e))return t}function $s(t,e){if(t==="change")return e}var Ws=!1;if(je){var Qi;if(je){var Zi="oninput"in document;if(!Zi){var Fs=document.createElement("div");Fs.setAttribute("oninput","return;"),Zi=typeof Fs.oninput=="function"}Qi=Zi}else Qi=!1;Ws=Qi&&(!document.documentMode||9<document.documentMode)}function Ps(){Kn&&(Kn.detachEvent("onpropertychange",Is),Jn=Kn=null)}function Is(t){if(t.propertyName==="value"&&uu(Jn)){var e=[];Js(e,Jn,t,Bi(t)),Hs(ny,e)}}function ay(t,e,l){t==="focusin"?(Ps(),Kn=e,Jn=l,Kn.attachEvent("onpropertychange",Is)):t==="focusout"&&Ps()}function uy(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return uu(Jn)}function iy(t,e){if(t==="click")return uu(e)}function ry(t,e){if(t==="input"||t==="change")return uu(e)}function cy(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ue=typeof Object.is=="function"?Object.is:cy;function $n(t,e){if(ue(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),n=Object.keys(e);if(l.length!==n.length)return!1;for(n=0;n<l.length;n++){var a=l[n];if(!Si.call(e,a)||!ue(t[a],e[a]))return!1}return!0}function tf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ef(t,e){var l=tf(t);t=0;for(var n;l;){if(l.nodeType===3){if(n=t+l.textContent.length,t<=e&&n>=e)return{node:l,offset:e-t};t=n}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=tf(l)}}function lf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?lf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function nf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Pa(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Pa(t.document)}return e}function ki(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var sy=je&&"documentMode"in document&&11>=document.documentMode,un=null,Ki=null,Wn=null,Ji=!1;function af(t,e,l){var n=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Ji||un==null||un!==Pa(n)||(n=un,"selectionStart"in n&&ki(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Wn&&$n(Wn,n)||(Wn=n,n=Qu(Ki,"onSelect"),0<n.length&&(e=new nu("onSelect","select",null,e,l),t.push({event:e,listeners:n}),e.target=un)))}function zl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var rn={animationend:zl("Animation","AnimationEnd"),animationiteration:zl("Animation","AnimationIteration"),animationstart:zl("Animation","AnimationStart"),transitionrun:zl("Transition","TransitionRun"),transitionstart:zl("Transition","TransitionStart"),transitioncancel:zl("Transition","TransitionCancel"),transitionend:zl("Transition","TransitionEnd")},$i={},uf={};je&&(uf=document.createElement("div").style,"AnimationEvent"in window||(delete rn.animationend.animation,delete rn.animationiteration.animation,delete rn.animationstart.animation),"TransitionEvent"in window||delete rn.transitionend.transition);function Cl(t){if($i[t])return $i[t];if(!rn[t])return t;var e=rn[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in uf)return $i[t]=e[l];return t}var rf=Cl("animationend"),cf=Cl("animationiteration"),sf=Cl("animationstart"),fy=Cl("transitionrun"),oy=Cl("transitionstart"),hy=Cl("transitioncancel"),ff=Cl("transitionend"),of=new Map,Wi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Wi.push("scrollEnd");function Te(t,e){of.set(t,e),Dl(e,[t])}var hf=new WeakMap;function ye(t,e){if(typeof t=="object"&&t!==null){var l=hf.get(t);return l!==void 0?l:(e={value:t,source:e,stack:Os(e)},hf.set(t,e),e)}return{value:t,source:e,stack:Os(e)}}var pe=[],cn=0,Fi=0;function iu(){for(var t=cn,e=Fi=cn=0;e<t;){var l=pe[e];pe[e++]=null;var n=pe[e];pe[e++]=null;var a=pe[e];pe[e++]=null;var i=pe[e];if(pe[e++]=null,n!==null&&a!==null){var f=n.pending;f===null?a.next=a:(a.next=f.next,f.next=a),n.pending=a}i!==0&&df(l,a,i)}}function ru(t,e,l,n){pe[cn++]=t,pe[cn++]=e,pe[cn++]=l,pe[cn++]=n,Fi|=n,t.lanes|=n,t=t.alternate,t!==null&&(t.lanes|=n)}function Pi(t,e,l,n){return ru(t,e,l,n),cu(t)}function sn(t,e){return ru(t,null,null,e),cu(t)}function df(t,e,l){t.lanes|=l;var n=t.alternate;n!==null&&(n.lanes|=l);for(var a=!1,i=t.return;i!==null;)i.childLanes|=l,n=i.alternate,n!==null&&(n.childLanes|=l),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(a=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,a&&e!==null&&(a=31-ae(l),t=i.hiddenUpdates,n=t[a],n===null?t[a]=[e]:n.push(e),e.lane=l|536870912),i):null}function cu(t){if(50<Ea)throw Ea=0,ac=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var fn={};function dy(t,e,l,n){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ie(t,e,l,n){return new dy(t,e,l,n)}function Ii(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ye(t,e){var l=t.alternate;return l===null?(l=ie(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function mf(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function su(t,e,l,n,a,i){var f=0;if(n=t,typeof t=="function")Ii(t)&&(f=1);else if(typeof t=="string")f=yp(t,l,$.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case dt:return t=ie(31,l,e,a),t.elementType=dt,t.lanes=i,t;case U:return Ul(l.children,a,i,e);case L:f=8,a|=24;break;case V:return t=ie(12,l,e,a|2),t.elementType=V,t.lanes=i,t;case J:return t=ie(13,l,e,a),t.elementType=J,t.lanes=i,t;case _t:return t=ie(19,l,e,a),t.elementType=_t,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case B:case Q:f=10;break t;case k:f=9;break t;case P:f=11;break t;case Tt:f=14;break t;case et:f=16,n=null;break t}f=29,l=Error(s(130,t===null?"null":typeof t,"")),n=null}return e=ie(f,l,e,a),e.elementType=t,e.type=n,e.lanes=i,e}function Ul(t,e,l,n){return t=ie(7,t,n,e),t.lanes=l,t}function tr(t,e,l){return t=ie(6,t,null,e),t.lanes=l,t}function er(t,e,l){return e=ie(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var on=[],hn=0,fu=null,ou=0,ve=[],ge=0,Bl=null,Xe=1,Ve="";function Hl(t,e){on[hn++]=ou,on[hn++]=fu,fu=t,ou=e}function yf(t,e,l){ve[ge++]=Xe,ve[ge++]=Ve,ve[ge++]=Bl,Bl=t;var n=Xe;t=Ve;var a=32-ae(n)-1;n&=~(1<<a),l+=1;var i=32-ae(e)+a;if(30<i){var f=a-a%5;i=(n&(1<<f)-1).toString(32),n>>=f,a-=f,Xe=1<<32-ae(e)+a|l<<a|n,Ve=i+t}else Xe=1<<i|l<<a|n,Ve=t}function lr(t){t.return!==null&&(Hl(t,1),yf(t,1,0))}function nr(t){for(;t===fu;)fu=on[--hn],on[hn]=null,ou=on[--hn],on[hn]=null;for(;t===Bl;)Bl=ve[--ge],ve[ge]=null,Ve=ve[--ge],ve[ge]=null,Xe=ve[--ge],ve[ge]=null}var Wt=null,Nt=null,ht=!1,ql=null,Ne=!1,ar=Error(s(519));function Ll(t){var e=Error(s(418,""));throw In(ye(e,t)),ar}function pf(t){var e=t.stateNode,l=t.type,n=t.memoizedProps;switch(e[kt]=t,e[Ft]=n,l){case"dialog":rt("cancel",e),rt("close",e);break;case"iframe":case"object":case"embed":rt("load",e);break;case"video":case"audio":for(l=0;l<Ta.length;l++)rt(Ta[l],e);break;case"source":rt("error",e);break;case"img":case"image":case"link":rt("error",e),rt("load",e);break;case"details":rt("toggle",e);break;case"input":rt("invalid",e),Ds(e,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),Fa(e);break;case"select":rt("invalid",e);break;case"textarea":rt("invalid",e),zs(e,n.value,n.defaultValue,n.children),Fa(e)}l=n.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||n.suppressHydrationWarning===!0||Uh(e.textContent,l)?(n.popover!=null&&(rt("beforetoggle",e),rt("toggle",e)),n.onScroll!=null&&rt("scroll",e),n.onScrollEnd!=null&&rt("scrollend",e),n.onClick!=null&&(e.onclick=Zu),e=!0):e=!1,e||Ll(t)}function vf(t){for(Wt=t.return;Wt;)switch(Wt.tag){case 5:case 13:Ne=!1;return;case 27:case 3:Ne=!0;return;default:Wt=Wt.return}}function Fn(t){if(t!==Wt)return!1;if(!ht)return vf(t),ht=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||Sc(t.type,t.memoizedProps)),l=!l),l&&Nt&&Ll(t),vf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Nt=Ae(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Nt=null}}else e===27?(e=Nt,Sl(t.type)?(t=xc,xc=null,Nt=t):Nt=e):Nt=Wt?Ae(t.stateNode.nextSibling):null;return!0}function Pn(){Nt=Wt=null,ht=!1}function gf(){var t=ql;return t!==null&&(ee===null?ee=t:ee.push.apply(ee,t),ql=null),t}function In(t){ql===null?ql=[t]:ql.push(t)}var ur=H(null),jl=null,Ge=null;function ul(t,e,l){j(ur,e._currentValue),e._currentValue=l}function Qe(t){t._currentValue=ur.current,G(ur)}function ir(t,e,l){for(;t!==null;){var n=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,n!==null&&(n.childLanes|=e)):n!==null&&(n.childLanes&e)!==e&&(n.childLanes|=e),t===l)break;t=t.return}}function rr(t,e,l,n){var a=t.child;for(a!==null&&(a.return=t);a!==null;){var i=a.dependencies;if(i!==null){var f=a.child;i=i.firstContext;t:for(;i!==null;){var h=i;i=a;for(var y=0;y<e.length;y++)if(h.context===e[y]){i.lanes|=l,h=i.alternate,h!==null&&(h.lanes|=l),ir(i.return,l,t),n||(f=null);break t}i=h.next}}else if(a.tag===18){if(f=a.return,f===null)throw Error(s(341));f.lanes|=l,i=f.alternate,i!==null&&(i.lanes|=l),ir(f,l,t),f=null}else f=a.child;if(f!==null)f.return=a;else for(f=a;f!==null;){if(f===t){f=null;break}if(a=f.sibling,a!==null){a.return=f.return,f=a;break}f=f.return}a=f}}function ta(t,e,l,n){t=null;for(var a=e,i=!1;a!==null;){if(!i){if((a.flags&524288)!==0)i=!0;else if((a.flags&262144)!==0)break}if(a.tag===10){var f=a.alternate;if(f===null)throw Error(s(387));if(f=f.memoizedProps,f!==null){var h=a.type;ue(a.pendingProps.value,f.value)||(t!==null?t.push(h):t=[h])}}else if(a===le.current){if(f=a.alternate,f===null)throw Error(s(387));f.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(t!==null?t.push(Ma):t=[Ma])}a=a.return}t!==null&&rr(e,t,l,n),e.flags|=262144}function hu(t){for(t=t.firstContext;t!==null;){if(!ue(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Yl(t){jl=t,Ge=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Kt(t){return bf(jl,t)}function du(t,e){return jl===null&&Yl(t),bf(t,e)}function bf(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Ge===null){if(t===null)throw Error(s(308));Ge=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ge=Ge.next=e;return l}var my=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},yy=r.unstable_scheduleCallback,py=r.unstable_NormalPriority,Bt={$$typeof:Q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function cr(){return{controller:new my,data:new Map,refCount:0}}function ea(t){t.refCount--,t.refCount===0&&yy(py,function(){t.controller.abort()})}var la=null,sr=0,dn=0,mn=null;function vy(t,e){if(la===null){var l=la=[];sr=0,dn=oc(),mn={status:"pending",value:void 0,then:function(n){l.push(n)}}}return sr++,e.then(Sf,Sf),e}function Sf(){if(--sr===0&&la!==null){mn!==null&&(mn.status="fulfilled");var t=la;la=null,dn=0,mn=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function gy(t,e){var l=[],n={status:"pending",value:null,reason:null,then:function(a){l.push(a)}};return t.then(function(){n.status="fulfilled",n.value=e;for(var a=0;a<l.length;a++)(0,l[a])(e)},function(a){for(n.status="rejected",n.reason=a,a=0;a<l.length;a++)(0,l[a])(void 0)}),n}var Ef=M.S;M.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&vy(t,e),Ef!==null&&Ef(t,e)};var Xl=H(null);function fr(){var t=Xl.current;return t!==null?t:Et.pooledCache}function mu(t,e){e===null?j(Xl,Xl.current):j(Xl,e.pool)}function _f(){var t=fr();return t===null?null:{parent:Bt._currentValue,pool:t}}var na=Error(s(460)),Tf=Error(s(474)),yu=Error(s(542)),or={then:function(){}};function xf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function pu(){}function Af(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(pu,pu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Of(t),t;default:if(typeof e.status=="string")e.then(pu,pu);else{if(t=Et,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(n){if(e.status==="pending"){var a=e;a.status="fulfilled",a.value=n}},function(n){if(e.status==="pending"){var a=e;a.status="rejected",a.reason=n}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Of(t),t}throw aa=e,na}}var aa=null;function Rf(){if(aa===null)throw Error(s(459));var t=aa;return aa=null,t}function Of(t){if(t===na||t===yu)throw Error(s(483))}var il=!1;function hr(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function dr(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function rl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function cl(t,e,l){var n=t.updateQueue;if(n===null)return null;if(n=n.shared,(mt&2)!==0){var a=n.pending;return a===null?e.next=e:(e.next=a.next,a.next=e),n.pending=e,e=cu(t),df(t,null,l),e}return ru(t,n,e,l),cu(t)}function ua(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,bs(t,l)}}function mr(t,e){var l=t.updateQueue,n=t.alternate;if(n!==null&&(n=n.updateQueue,l===n)){var a=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var f={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?a=i=f:i=i.next=f,l=l.next}while(l!==null);i===null?a=i=e:i=i.next=e}else a=i=e;l={baseState:n.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:n.shared,callbacks:n.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var yr=!1;function ia(){if(yr){var t=mn;if(t!==null)throw t}}function ra(t,e,l,n){yr=!1;var a=t.updateQueue;il=!1;var i=a.firstBaseUpdate,f=a.lastBaseUpdate,h=a.shared.pending;if(h!==null){a.shared.pending=null;var y=h,T=y.next;y.next=null,f===null?i=T:f.next=T,f=y;var N=t.alternate;N!==null&&(N=N.updateQueue,h=N.lastBaseUpdate,h!==f&&(h===null?N.firstBaseUpdate=T:h.next=T,N.lastBaseUpdate=y))}if(i!==null){var z=a.baseState;f=0,N=T=y=null,h=i;do{var A=h.lane&-536870913,R=A!==h.lane;if(R?(st&A)===A:(n&A)===A){A!==0&&A===dn&&(yr=!0),N!==null&&(N=N.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});t:{var tt=t,W=h;A=e;var gt=l;switch(W.tag){case 1:if(tt=W.payload,typeof tt=="function"){z=tt.call(gt,z,A);break t}z=tt;break t;case 3:tt.flags=tt.flags&-65537|128;case 0:if(tt=W.payload,A=typeof tt=="function"?tt.call(gt,z,A):tt,A==null)break t;z=E({},z,A);break t;case 2:il=!0}}A=h.callback,A!==null&&(t.flags|=64,R&&(t.flags|=8192),R=a.callbacks,R===null?a.callbacks=[A]:R.push(A))}else R={lane:A,tag:h.tag,payload:h.payload,callback:h.callback,next:null},N===null?(T=N=R,y=z):N=N.next=R,f|=A;if(h=h.next,h===null){if(h=a.shared.pending,h===null)break;R=h,h=R.next,R.next=null,a.lastBaseUpdate=R,a.shared.pending=null}}while(!0);N===null&&(y=z),a.baseState=y,a.firstBaseUpdate=T,a.lastBaseUpdate=N,i===null&&(a.shared.lanes=0),pl|=f,t.lanes=f,t.memoizedState=z}}function Nf(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function Mf(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)Nf(l[t],e)}var yn=H(null),vu=H(0);function Df(t,e){t=Fe,j(vu,t),j(yn,e),Fe=t|e.baseLanes}function pr(){j(vu,Fe),j(yn,yn.current)}function vr(){Fe=vu.current,G(yn),G(vu)}var sl=0,at=null,pt=null,zt=null,gu=!1,pn=!1,Vl=!1,bu=0,ca=0,vn=null,by=0;function Dt(){throw Error(s(321))}function gr(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!ue(t[l],e[l]))return!1;return!0}function br(t,e,l,n,a,i){return sl=i,at=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,M.H=t===null||t.memoizedState===null?mo:yo,Vl=!1,i=l(n,a),Vl=!1,pn&&(i=zf(e,l,n,a)),wf(t),i}function wf(t){M.H=Au;var e=pt!==null&&pt.next!==null;if(sl=0,zt=pt=at=null,gu=!1,ca=0,vn=null,e)throw Error(s(300));t===null||Lt||(t=t.dependencies,t!==null&&hu(t)&&(Lt=!0))}function zf(t,e,l,n){at=t;var a=0;do{if(pn&&(vn=null),ca=0,pn=!1,25<=a)throw Error(s(301));if(a+=1,zt=pt=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}M.H=Ry,i=e(l,n)}while(pn);return i}function Sy(){var t=M.H,e=t.useState()[0];return e=typeof e.then=="function"?sa(e):e,t=t.useState()[0],(pt!==null?pt.memoizedState:null)!==t&&(at.flags|=1024),e}function Sr(){var t=bu!==0;return bu=0,t}function Er(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function _r(t){if(gu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}gu=!1}sl=0,zt=pt=at=null,pn=!1,ca=bu=0,vn=null}function It(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return zt===null?at.memoizedState=zt=t:zt=zt.next=t,zt}function Ct(){if(pt===null){var t=at.alternate;t=t!==null?t.memoizedState:null}else t=pt.next;var e=zt===null?at.memoizedState:zt.next;if(e!==null)zt=e,pt=t;else{if(t===null)throw at.alternate===null?Error(s(467)):Error(s(310));pt=t,t={memoizedState:pt.memoizedState,baseState:pt.baseState,baseQueue:pt.baseQueue,queue:pt.queue,next:null},zt===null?at.memoizedState=zt=t:zt=zt.next=t}return zt}function Tr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function sa(t){var e=ca;return ca+=1,vn===null&&(vn=[]),t=Af(vn,t,e),e=at,(zt===null?e.memoizedState:zt.next)===null&&(e=e.alternate,M.H=e===null||e.memoizedState===null?mo:yo),t}function Su(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return sa(t);if(t.$$typeof===Q)return Kt(t)}throw Error(s(438,String(t)))}function xr(t){var e=null,l=at.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var n=at.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(e={data:n.data.map(function(a){return a.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=Tr(),at.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),n=0;n<t;n++)l[n]=$t;return e.index++,l}function Ze(t,e){return typeof e=="function"?e(t):e}function Eu(t){var e=Ct();return Ar(e,pt,t)}function Ar(t,e,l){var n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=l;var a=t.baseQueue,i=n.pending;if(i!==null){if(a!==null){var f=a.next;a.next=i.next,i.next=f}e.baseQueue=a=i,n.pending=null}if(i=t.baseState,a===null)t.memoizedState=i;else{e=a.next;var h=f=null,y=null,T=e,N=!1;do{var z=T.lane&-536870913;if(z!==T.lane?(st&z)===z:(sl&z)===z){var A=T.revertLane;if(A===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null}),z===dn&&(N=!0);else if((sl&A)===A){T=T.next,A===dn&&(N=!0);continue}else z={lane:0,revertLane:T.revertLane,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null},y===null?(h=y=z,f=i):y=y.next=z,at.lanes|=A,pl|=A;z=T.action,Vl&&l(i,z),i=T.hasEagerState?T.eagerState:l(i,z)}else A={lane:z,revertLane:T.revertLane,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null},y===null?(h=y=A,f=i):y=y.next=A,at.lanes|=z,pl|=z;T=T.next}while(T!==null&&T!==e);if(y===null?f=i:y.next=h,!ue(i,t.memoizedState)&&(Lt=!0,N&&(l=mn,l!==null)))throw l;t.memoizedState=i,t.baseState=f,t.baseQueue=y,n.lastRenderedState=i}return a===null&&(n.lanes=0),[t.memoizedState,n.dispatch]}function Rr(t){var e=Ct(),l=e.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=t;var n=l.dispatch,a=l.pending,i=e.memoizedState;if(a!==null){l.pending=null;var f=a=a.next;do i=t(i,f.action),f=f.next;while(f!==a);ue(i,e.memoizedState)||(Lt=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),l.lastRenderedState=i}return[i,n]}function Cf(t,e,l){var n=at,a=Ct(),i=ht;if(i){if(l===void 0)throw Error(s(407));l=l()}else l=e();var f=!ue((pt||a).memoizedState,l);f&&(a.memoizedState=l,Lt=!0),a=a.queue;var h=Hf.bind(null,n,a,t);if(fa(2048,8,h,[t]),a.getSnapshot!==e||f||zt!==null&&zt.memoizedState.tag&1){if(n.flags|=2048,gn(9,_u(),Bf.bind(null,n,a,l,e),null),Et===null)throw Error(s(349));i||(sl&124)!==0||Uf(n,e,l)}return l}function Uf(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=at.updateQueue,e===null?(e=Tr(),at.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function Bf(t,e,l,n){e.value=l,e.getSnapshot=n,qf(e)&&Lf(t)}function Hf(t,e,l){return l(function(){qf(e)&&Lf(t)})}function qf(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!ue(t,l)}catch{return!0}}function Lf(t){var e=sn(t,2);e!==null&&oe(e,t,2)}function Or(t){var e=It();if(typeof t=="function"){var l=t;if(t=l(),Vl){ll(!0);try{l()}finally{ll(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:t},e}function jf(t,e,l,n){return t.baseState=l,Ar(t,pt,typeof n=="function"?n:Ze)}function Ey(t,e,l,n,a){if(xu(t))throw Error(s(485));if(t=e.action,t!==null){var i={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){i.listeners.push(f)}};M.T!==null?l(!0):i.isTransition=!1,n(i),l=e.pending,l===null?(i.next=e.pending=i,Yf(e,i)):(i.next=l.next,e.pending=l.next=i)}}function Yf(t,e){var l=e.action,n=e.payload,a=t.state;if(e.isTransition){var i=M.T,f={};M.T=f;try{var h=l(a,n),y=M.S;y!==null&&y(f,h),Xf(t,e,h)}catch(T){Nr(t,e,T)}finally{M.T=i}}else try{i=l(a,n),Xf(t,e,i)}catch(T){Nr(t,e,T)}}function Xf(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(n){Vf(t,e,n)},function(n){return Nr(t,e,n)}):Vf(t,e,l)}function Vf(t,e,l){e.status="fulfilled",e.value=l,Gf(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,Yf(t,l)))}function Nr(t,e,l){var n=t.pending;if(t.pending=null,n!==null){n=n.next;do e.status="rejected",e.reason=l,Gf(e),e=e.next;while(e!==n)}t.action=null}function Gf(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Qf(t,e){return e}function Zf(t,e){if(ht){var l=Et.formState;if(l!==null){t:{var n=at;if(ht){if(Nt){e:{for(var a=Nt,i=Ne;a.nodeType!==8;){if(!i){a=null;break e}if(a=Ae(a.nextSibling),a===null){a=null;break e}}i=a.data,a=i==="F!"||i==="F"?a:null}if(a){Nt=Ae(a.nextSibling),n=a.data==="F!";break t}}Ll(n)}n=!1}n&&(e=l[0])}}return l=It(),l.memoizedState=l.baseState=e,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qf,lastRenderedState:e},l.queue=n,l=fo.bind(null,at,n),n.dispatch=l,n=Or(!1),i=Cr.bind(null,at,!1,n.queue),n=It(),a={state:e,dispatch:null,action:t,pending:null},n.queue=a,l=Ey.bind(null,at,a,i,l),a.dispatch=l,n.memoizedState=t,[e,l,!1]}function kf(t){var e=Ct();return Kf(e,pt,t)}function Kf(t,e,l){if(e=Ar(t,e,Qf)[0],t=Eu(Ze)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var n=sa(e)}catch(f){throw f===na?yu:f}else n=e;e=Ct();var a=e.queue,i=a.dispatch;return l!==e.memoizedState&&(at.flags|=2048,gn(9,_u(),_y.bind(null,a,l),null)),[n,i,t]}function _y(t,e){t.action=e}function Jf(t){var e=Ct(),l=pt;if(l!==null)return Kf(e,l,t);Ct(),e=e.memoizedState,l=Ct();var n=l.queue.dispatch;return l.memoizedState=t,[e,n,!1]}function gn(t,e,l,n){return t={tag:t,create:l,deps:n,inst:e,next:null},e=at.updateQueue,e===null&&(e=Tr(),at.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(n=l.next,l.next=t,t.next=n,e.lastEffect=t),t}function _u(){return{destroy:void 0,resource:void 0}}function $f(){return Ct().memoizedState}function Tu(t,e,l,n){var a=It();n=n===void 0?null:n,at.flags|=t,a.memoizedState=gn(1|e,_u(),l,n)}function fa(t,e,l,n){var a=Ct();n=n===void 0?null:n;var i=a.memoizedState.inst;pt!==null&&n!==null&&gr(n,pt.memoizedState.deps)?a.memoizedState=gn(e,i,l,n):(at.flags|=t,a.memoizedState=gn(1|e,i,l,n))}function Wf(t,e){Tu(8390656,8,t,e)}function Ff(t,e){fa(2048,8,t,e)}function Pf(t,e){return fa(4,2,t,e)}function If(t,e){return fa(4,4,t,e)}function to(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function eo(t,e,l){l=l!=null?l.concat([t]):null,fa(4,4,to.bind(null,e,t),l)}function Mr(){}function lo(t,e){var l=Ct();e=e===void 0?null:e;var n=l.memoizedState;return e!==null&&gr(e,n[1])?n[0]:(l.memoizedState=[t,e],t)}function no(t,e){var l=Ct();e=e===void 0?null:e;var n=l.memoizedState;if(e!==null&&gr(e,n[1]))return n[0];if(n=t(),Vl){ll(!0);try{t()}finally{ll(!1)}}return l.memoizedState=[n,e],n}function Dr(t,e,l){return l===void 0||(sl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=rh(),at.lanes|=t,pl|=t,l)}function ao(t,e,l,n){return ue(l,e)?l:yn.current!==null?(t=Dr(t,l,n),ue(t,e)||(Lt=!0),t):(sl&42)===0?(Lt=!0,t.memoizedState=l):(t=rh(),at.lanes|=t,pl|=t,e)}function uo(t,e,l,n,a){var i=X.p;X.p=i!==0&&8>i?i:8;var f=M.T,h={};M.T=h,Cr(t,!1,e,l);try{var y=a(),T=M.S;if(T!==null&&T(h,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var N=gy(y,n);oa(t,e,N,fe(t))}else oa(t,e,n,fe(t))}catch(z){oa(t,e,{then:function(){},status:"rejected",reason:z},fe())}finally{X.p=i,M.T=f}}function Ty(){}function wr(t,e,l,n){if(t.tag!==5)throw Error(s(476));var a=io(t).queue;uo(t,a,e,I,l===null?Ty:function(){return ro(t),l(n)})}function io(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:I,baseState:I,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:I},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function ro(t){var e=io(t).next.queue;oa(t,e,{},fe())}function zr(){return Kt(Ma)}function co(){return Ct().memoizedState}function so(){return Ct().memoizedState}function xy(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=fe();t=rl(l);var n=cl(e,t,l);n!==null&&(oe(n,e,l),ua(n,e,l)),e={cache:cr()},t.payload=e;return}e=e.return}}function Ay(t,e,l){var n=fe();l={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},xu(t)?oo(e,l):(l=Pi(t,e,l,n),l!==null&&(oe(l,t,n),ho(l,e,n)))}function fo(t,e,l){var n=fe();oa(t,e,l,n)}function oa(t,e,l,n){var a={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(xu(t))oo(e,a);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var f=e.lastRenderedState,h=i(f,l);if(a.hasEagerState=!0,a.eagerState=h,ue(h,f))return ru(t,e,a,0),Et===null&&iu(),!1}catch{}finally{}if(l=Pi(t,e,a,n),l!==null)return oe(l,t,n),ho(l,e,n),!0}return!1}function Cr(t,e,l,n){if(n={lane:2,revertLane:oc(),action:n,hasEagerState:!1,eagerState:null,next:null},xu(t)){if(e)throw Error(s(479))}else e=Pi(t,l,n,2),e!==null&&oe(e,t,2)}function xu(t){var e=t.alternate;return t===at||e!==null&&e===at}function oo(t,e){pn=gu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function ho(t,e,l){if((l&4194048)!==0){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,bs(t,l)}}var Au={readContext:Kt,use:Su,useCallback:Dt,useContext:Dt,useEffect:Dt,useImperativeHandle:Dt,useLayoutEffect:Dt,useInsertionEffect:Dt,useMemo:Dt,useReducer:Dt,useRef:Dt,useState:Dt,useDebugValue:Dt,useDeferredValue:Dt,useTransition:Dt,useSyncExternalStore:Dt,useId:Dt,useHostTransitionStatus:Dt,useFormState:Dt,useActionState:Dt,useOptimistic:Dt,useMemoCache:Dt,useCacheRefresh:Dt},mo={readContext:Kt,use:Su,useCallback:function(t,e){return It().memoizedState=[t,e===void 0?null:e],t},useContext:Kt,useEffect:Wf,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Tu(4194308,4,to.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Tu(4194308,4,t,e)},useInsertionEffect:function(t,e){Tu(4,2,t,e)},useMemo:function(t,e){var l=It();e=e===void 0?null:e;var n=t();if(Vl){ll(!0);try{t()}finally{ll(!1)}}return l.memoizedState=[n,e],n},useReducer:function(t,e,l){var n=It();if(l!==void 0){var a=l(e);if(Vl){ll(!0);try{l(e)}finally{ll(!1)}}}else a=e;return n.memoizedState=n.baseState=a,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:a},n.queue=t,t=t.dispatch=Ay.bind(null,at,t),[n.memoizedState,t]},useRef:function(t){var e=It();return t={current:t},e.memoizedState=t},useState:function(t){t=Or(t);var e=t.queue,l=fo.bind(null,at,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:Mr,useDeferredValue:function(t,e){var l=It();return Dr(l,t,e)},useTransition:function(){var t=Or(!1);return t=uo.bind(null,at,t.queue,!0,!1),It().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var n=at,a=It();if(ht){if(l===void 0)throw Error(s(407));l=l()}else{if(l=e(),Et===null)throw Error(s(349));(st&124)!==0||Uf(n,e,l)}a.memoizedState=l;var i={value:l,getSnapshot:e};return a.queue=i,Wf(Hf.bind(null,n,i,t),[t]),n.flags|=2048,gn(9,_u(),Bf.bind(null,n,i,l,e),null),l},useId:function(){var t=It(),e=Et.identifierPrefix;if(ht){var l=Ve,n=Xe;l=(n&~(1<<32-ae(n)-1)).toString(32)+l,e="«"+e+"R"+l,l=bu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=by++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:zr,useFormState:Zf,useActionState:Zf,useOptimistic:function(t){var e=It();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=Cr.bind(null,at,!0,l),l.dispatch=e,[t,e]},useMemoCache:xr,useCacheRefresh:function(){return It().memoizedState=xy.bind(null,at)}},yo={readContext:Kt,use:Su,useCallback:lo,useContext:Kt,useEffect:Ff,useImperativeHandle:eo,useInsertionEffect:Pf,useLayoutEffect:If,useMemo:no,useReducer:Eu,useRef:$f,useState:function(){return Eu(Ze)},useDebugValue:Mr,useDeferredValue:function(t,e){var l=Ct();return ao(l,pt.memoizedState,t,e)},useTransition:function(){var t=Eu(Ze)[0],e=Ct().memoizedState;return[typeof t=="boolean"?t:sa(t),e]},useSyncExternalStore:Cf,useId:co,useHostTransitionStatus:zr,useFormState:kf,useActionState:kf,useOptimistic:function(t,e){var l=Ct();return jf(l,pt,t,e)},useMemoCache:xr,useCacheRefresh:so},Ry={readContext:Kt,use:Su,useCallback:lo,useContext:Kt,useEffect:Ff,useImperativeHandle:eo,useInsertionEffect:Pf,useLayoutEffect:If,useMemo:no,useReducer:Rr,useRef:$f,useState:function(){return Rr(Ze)},useDebugValue:Mr,useDeferredValue:function(t,e){var l=Ct();return pt===null?Dr(l,t,e):ao(l,pt.memoizedState,t,e)},useTransition:function(){var t=Rr(Ze)[0],e=Ct().memoizedState;return[typeof t=="boolean"?t:sa(t),e]},useSyncExternalStore:Cf,useId:co,useHostTransitionStatus:zr,useFormState:Jf,useActionState:Jf,useOptimistic:function(t,e){var l=Ct();return pt!==null?jf(l,pt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:xr,useCacheRefresh:so},bn=null,ha=0;function Ru(t){var e=ha;return ha+=1,bn===null&&(bn=[]),Af(bn,t,e)}function da(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Ou(t,e){throw e.$$typeof===C?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function po(t){var e=t._init;return e(t._payload)}function vo(t){function e(S,g){if(t){var _=S.deletions;_===null?(S.deletions=[g],S.flags|=16):_.push(g)}}function l(S,g){if(!t)return null;for(;g!==null;)e(S,g),g=g.sibling;return null}function n(S){for(var g=new Map;S!==null;)S.key!==null?g.set(S.key,S):g.set(S.index,S),S=S.sibling;return g}function a(S,g){return S=Ye(S,g),S.index=0,S.sibling=null,S}function i(S,g,_){return S.index=_,t?(_=S.alternate,_!==null?(_=_.index,_<g?(S.flags|=67108866,g):_):(S.flags|=67108866,g)):(S.flags|=1048576,g)}function f(S){return t&&S.alternate===null&&(S.flags|=67108866),S}function h(S,g,_,w){return g===null||g.tag!==6?(g=tr(_,S.mode,w),g.return=S,g):(g=a(g,_),g.return=S,g)}function y(S,g,_,w){var Z=_.type;return Z===U?N(S,g,_.props.children,w,_.key):g!==null&&(g.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===et&&po(Z)===g.type)?(g=a(g,_.props),da(g,_),g.return=S,g):(g=su(_.type,_.key,_.props,null,S.mode,w),da(g,_),g.return=S,g)}function T(S,g,_,w){return g===null||g.tag!==4||g.stateNode.containerInfo!==_.containerInfo||g.stateNode.implementation!==_.implementation?(g=er(_,S.mode,w),g.return=S,g):(g=a(g,_.children||[]),g.return=S,g)}function N(S,g,_,w,Z){return g===null||g.tag!==7?(g=Ul(_,S.mode,w,Z),g.return=S,g):(g=a(g,_),g.return=S,g)}function z(S,g,_){if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return g=tr(""+g,S.mode,_),g.return=S,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case D:return _=su(g.type,g.key,g.props,null,S.mode,_),da(_,g),_.return=S,_;case Y:return g=er(g,S.mode,_),g.return=S,g;case et:var w=g._init;return g=w(g._payload),z(S,g,_)}if(Zt(g)||Qt(g))return g=Ul(g,S.mode,_,null),g.return=S,g;if(typeof g.then=="function")return z(S,Ru(g),_);if(g.$$typeof===Q)return z(S,du(S,g),_);Ou(S,g)}return null}function A(S,g,_,w){var Z=g!==null?g.key:null;if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return Z!==null?null:h(S,g,""+_,w);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case D:return _.key===Z?y(S,g,_,w):null;case Y:return _.key===Z?T(S,g,_,w):null;case et:return Z=_._init,_=Z(_._payload),A(S,g,_,w)}if(Zt(_)||Qt(_))return Z!==null?null:N(S,g,_,w,null);if(typeof _.then=="function")return A(S,g,Ru(_),w);if(_.$$typeof===Q)return A(S,g,du(S,_),w);Ou(S,_)}return null}function R(S,g,_,w,Z){if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return S=S.get(_)||null,h(g,S,""+w,Z);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case D:return S=S.get(w.key===null?_:w.key)||null,y(g,S,w,Z);case Y:return S=S.get(w.key===null?_:w.key)||null,T(g,S,w,Z);case et:var ut=w._init;return w=ut(w._payload),R(S,g,_,w,Z)}if(Zt(w)||Qt(w))return S=S.get(_)||null,N(g,S,w,Z,null);if(typeof w.then=="function")return R(S,g,_,Ru(w),Z);if(w.$$typeof===Q)return R(S,g,_,du(g,w),Z);Ou(g,w)}return null}function tt(S,g,_,w){for(var Z=null,ut=null,K=g,F=g=0,Yt=null;K!==null&&F<_.length;F++){K.index>F?(Yt=K,K=null):Yt=K.sibling;var ot=A(S,K,_[F],w);if(ot===null){K===null&&(K=Yt);break}t&&K&&ot.alternate===null&&e(S,K),g=i(ot,g,F),ut===null?Z=ot:ut.sibling=ot,ut=ot,K=Yt}if(F===_.length)return l(S,K),ht&&Hl(S,F),Z;if(K===null){for(;F<_.length;F++)K=z(S,_[F],w),K!==null&&(g=i(K,g,F),ut===null?Z=K:ut.sibling=K,ut=K);return ht&&Hl(S,F),Z}for(K=n(K);F<_.length;F++)Yt=R(K,S,F,_[F],w),Yt!==null&&(t&&Yt.alternate!==null&&K.delete(Yt.key===null?F:Yt.key),g=i(Yt,g,F),ut===null?Z=Yt:ut.sibling=Yt,ut=Yt);return t&&K.forEach(function(Al){return e(S,Al)}),ht&&Hl(S,F),Z}function W(S,g,_,w){if(_==null)throw Error(s(151));for(var Z=null,ut=null,K=g,F=g=0,Yt=null,ot=_.next();K!==null&&!ot.done;F++,ot=_.next()){K.index>F?(Yt=K,K=null):Yt=K.sibling;var Al=A(S,K,ot.value,w);if(Al===null){K===null&&(K=Yt);break}t&&K&&Al.alternate===null&&e(S,K),g=i(Al,g,F),ut===null?Z=Al:ut.sibling=Al,ut=Al,K=Yt}if(ot.done)return l(S,K),ht&&Hl(S,F),Z;if(K===null){for(;!ot.done;F++,ot=_.next())ot=z(S,ot.value,w),ot!==null&&(g=i(ot,g,F),ut===null?Z=ot:ut.sibling=ot,ut=ot);return ht&&Hl(S,F),Z}for(K=n(K);!ot.done;F++,ot=_.next())ot=R(K,S,F,ot.value,w),ot!==null&&(t&&ot.alternate!==null&&K.delete(ot.key===null?F:ot.key),g=i(ot,g,F),ut===null?Z=ot:ut.sibling=ot,ut=ot);return t&&K.forEach(function(Op){return e(S,Op)}),ht&&Hl(S,F),Z}function gt(S,g,_,w){if(typeof _=="object"&&_!==null&&_.type===U&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case D:t:{for(var Z=_.key;g!==null;){if(g.key===Z){if(Z=_.type,Z===U){if(g.tag===7){l(S,g.sibling),w=a(g,_.props.children),w.return=S,S=w;break t}}else if(g.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===et&&po(Z)===g.type){l(S,g.sibling),w=a(g,_.props),da(w,_),w.return=S,S=w;break t}l(S,g);break}else e(S,g);g=g.sibling}_.type===U?(w=Ul(_.props.children,S.mode,w,_.key),w.return=S,S=w):(w=su(_.type,_.key,_.props,null,S.mode,w),da(w,_),w.return=S,S=w)}return f(S);case Y:t:{for(Z=_.key;g!==null;){if(g.key===Z)if(g.tag===4&&g.stateNode.containerInfo===_.containerInfo&&g.stateNode.implementation===_.implementation){l(S,g.sibling),w=a(g,_.children||[]),w.return=S,S=w;break t}else{l(S,g);break}else e(S,g);g=g.sibling}w=er(_,S.mode,w),w.return=S,S=w}return f(S);case et:return Z=_._init,_=Z(_._payload),gt(S,g,_,w)}if(Zt(_))return tt(S,g,_,w);if(Qt(_)){if(Z=Qt(_),typeof Z!="function")throw Error(s(150));return _=Z.call(_),W(S,g,_,w)}if(typeof _.then=="function")return gt(S,g,Ru(_),w);if(_.$$typeof===Q)return gt(S,g,du(S,_),w);Ou(S,_)}return typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint"?(_=""+_,g!==null&&g.tag===6?(l(S,g.sibling),w=a(g,_),w.return=S,S=w):(l(S,g),w=tr(_,S.mode,w),w.return=S,S=w),f(S)):l(S,g)}return function(S,g,_,w){try{ha=0;var Z=gt(S,g,_,w);return bn=null,Z}catch(K){if(K===na||K===yu)throw K;var ut=ie(29,K,null,S.mode);return ut.lanes=w,ut.return=S,ut}finally{}}}var Sn=vo(!0),go=vo(!1),be=H(null),Me=null;function fl(t){var e=t.alternate;j(Ht,Ht.current&1),j(be,t),Me===null&&(e===null||yn.current!==null||e.memoizedState!==null)&&(Me=t)}function bo(t){if(t.tag===22){if(j(Ht,Ht.current),j(be,t),Me===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Me=t)}}else ol()}function ol(){j(Ht,Ht.current),j(be,be.current)}function ke(t){G(be),Me===t&&(Me=null),G(Ht)}var Ht=H(0);function Nu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Tc(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Ur(t,e,l,n){e=t.memoizedState,l=l(n,e),l=l==null?e:E({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Br={enqueueSetState:function(t,e,l){t=t._reactInternals;var n=fe(),a=rl(n);a.payload=e,l!=null&&(a.callback=l),e=cl(t,a,n),e!==null&&(oe(e,t,n),ua(e,t,n))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var n=fe(),a=rl(n);a.tag=1,a.payload=e,l!=null&&(a.callback=l),e=cl(t,a,n),e!==null&&(oe(e,t,n),ua(e,t,n))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=fe(),n=rl(l);n.tag=2,e!=null&&(n.callback=e),e=cl(t,n,l),e!==null&&(oe(e,t,l),ua(e,t,l))}};function So(t,e,l,n,a,i,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(n,i,f):e.prototype&&e.prototype.isPureReactComponent?!$n(l,n)||!$n(a,i):!0}function Eo(t,e,l,n){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,n),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,n),e.state!==t&&Br.enqueueReplaceState(e,e.state,null)}function Gl(t,e){var l=e;if("ref"in e){l={};for(var n in e)n!=="ref"&&(l[n]=e[n])}if(t=t.defaultProps){l===e&&(l=E({},l));for(var a in t)l[a]===void 0&&(l[a]=t[a])}return l}var Mu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function _o(t){Mu(t)}function To(t){console.error(t)}function xo(t){Mu(t)}function Du(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(n){setTimeout(function(){throw n})}}function Ao(t,e,l){try{var n=t.onCaughtError;n(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(a){setTimeout(function(){throw a})}}function Hr(t,e,l){return l=rl(l),l.tag=3,l.payload={element:null},l.callback=function(){Du(t,e)},l}function Ro(t){return t=rl(t),t.tag=3,t}function Oo(t,e,l,n){var a=l.type.getDerivedStateFromError;if(typeof a=="function"){var i=n.value;t.payload=function(){return a(i)},t.callback=function(){Ao(e,l,n)}}var f=l.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Ao(e,l,n),typeof a!="function"&&(vl===null?vl=new Set([this]):vl.add(this));var h=n.stack;this.componentDidCatch(n.value,{componentStack:h!==null?h:""})})}function Oy(t,e,l,n,a){if(l.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(e=l.alternate,e!==null&&ta(e,l,a,!0),l=be.current,l!==null){switch(l.tag){case 13:return Me===null?ic():l.alternate===null&&Mt===0&&(Mt=3),l.flags&=-257,l.flags|=65536,l.lanes=a,n===or?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([n]):e.add(n),cc(t,n,a)),!1;case 22:return l.flags|=65536,n===or?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([n])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([n]):l.add(n)),cc(t,n,a)),!1}throw Error(s(435,l.tag))}return cc(t,n,a),ic(),!1}if(ht)return e=be.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=a,n!==ar&&(t=Error(s(422),{cause:n}),In(ye(t,l)))):(n!==ar&&(e=Error(s(423),{cause:n}),In(ye(e,l))),t=t.current.alternate,t.flags|=65536,a&=-a,t.lanes|=a,n=ye(n,l),a=Hr(t.stateNode,n,a),mr(t,a),Mt!==4&&(Mt=2)),!1;var i=Error(s(520),{cause:n});if(i=ye(i,l),Sa===null?Sa=[i]:Sa.push(i),Mt!==4&&(Mt=2),e===null)return!0;n=ye(n,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=a&-a,l.lanes|=t,t=Hr(l.stateNode,n,t),mr(l,t),!1;case 1:if(e=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(vl===null||!vl.has(i))))return l.flags|=65536,a&=-a,l.lanes|=a,a=Ro(a),Oo(a,t,l,n),mr(l,a),!1}l=l.return}while(l!==null);return!1}var No=Error(s(461)),Lt=!1;function Xt(t,e,l,n){e.child=t===null?go(e,null,l,n):Sn(e,t.child,l,n)}function Mo(t,e,l,n,a){l=l.render;var i=e.ref;if("ref"in n){var f={};for(var h in n)h!=="ref"&&(f[h]=n[h])}else f=n;return Yl(e),n=br(t,e,l,f,i,a),h=Sr(),t!==null&&!Lt?(Er(t,e,a),Ke(t,e,a)):(ht&&h&&lr(e),e.flags|=1,Xt(t,e,n,a),e.child)}function Do(t,e,l,n,a){if(t===null){var i=l.type;return typeof i=="function"&&!Ii(i)&&i.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=i,wo(t,e,i,n,a)):(t=su(l.type,null,n,e,e.mode,a),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!Qr(t,a)){var f=i.memoizedProps;if(l=l.compare,l=l!==null?l:$n,l(f,n)&&t.ref===e.ref)return Ke(t,e,a)}return e.flags|=1,t=Ye(i,n),t.ref=e.ref,t.return=e,e.child=t}function wo(t,e,l,n,a){if(t!==null){var i=t.memoizedProps;if($n(i,n)&&t.ref===e.ref)if(Lt=!1,e.pendingProps=n=i,Qr(t,a))(t.flags&131072)!==0&&(Lt=!0);else return e.lanes=t.lanes,Ke(t,e,a)}return qr(t,e,l,n,a)}function zo(t,e,l){var n=e.pendingProps,a=n.children,i=t!==null?t.memoizedState:null;if(n.mode==="hidden"){if((e.flags&128)!==0){if(n=i!==null?i.baseLanes|l:l,t!==null){for(a=e.child=t.child,i=0;a!==null;)i=i|a.lanes|a.childLanes,a=a.sibling;e.childLanes=i&~n}else e.childLanes=0,e.child=null;return Co(t,e,n,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&mu(e,i!==null?i.cachePool:null),i!==null?Df(e,i):pr(),bo(e);else return e.lanes=e.childLanes=536870912,Co(t,e,i!==null?i.baseLanes|l:l,l)}else i!==null?(mu(e,i.cachePool),Df(e,i),ol(),e.memoizedState=null):(t!==null&&mu(e,null),pr(),ol());return Xt(t,e,a,l),e.child}function Co(t,e,l,n){var a=fr();return a=a===null?null:{parent:Bt._currentValue,pool:a},e.memoizedState={baseLanes:l,cachePool:a},t!==null&&mu(e,null),pr(),bo(e),t!==null&&ta(t,e,n,!0),null}function wu(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function qr(t,e,l,n,a){return Yl(e),l=br(t,e,l,n,void 0,a),n=Sr(),t!==null&&!Lt?(Er(t,e,a),Ke(t,e,a)):(ht&&n&&lr(e),e.flags|=1,Xt(t,e,l,a),e.child)}function Uo(t,e,l,n,a,i){return Yl(e),e.updateQueue=null,l=zf(e,n,l,a),wf(t),n=Sr(),t!==null&&!Lt?(Er(t,e,i),Ke(t,e,i)):(ht&&n&&lr(e),e.flags|=1,Xt(t,e,l,i),e.child)}function Bo(t,e,l,n,a){if(Yl(e),e.stateNode===null){var i=fn,f=l.contextType;typeof f=="object"&&f!==null&&(i=Kt(f)),i=new l(n,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Br,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=n,i.state=e.memoizedState,i.refs={},hr(e),f=l.contextType,i.context=typeof f=="object"&&f!==null?Kt(f):fn,i.state=e.memoizedState,f=l.getDerivedStateFromProps,typeof f=="function"&&(Ur(e,l,f,n),i.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(f=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),f!==i.state&&Br.enqueueReplaceState(i,i.state,null),ra(e,n,i,a),ia(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),n=!0}else if(t===null){i=e.stateNode;var h=e.memoizedProps,y=Gl(l,h);i.props=y;var T=i.context,N=l.contextType;f=fn,typeof N=="object"&&N!==null&&(f=Kt(N));var z=l.getDerivedStateFromProps;N=typeof z=="function"||typeof i.getSnapshotBeforeUpdate=="function",h=e.pendingProps!==h,N||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(h||T!==f)&&Eo(e,i,n,f),il=!1;var A=e.memoizedState;i.state=A,ra(e,n,i,a),ia(),T=e.memoizedState,h||A!==T||il?(typeof z=="function"&&(Ur(e,l,z,n),T=e.memoizedState),(y=il||So(e,l,y,n,A,T,f))?(N||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=n,e.memoizedState=T),i.props=n,i.state=T,i.context=f,n=y):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),n=!1)}else{i=e.stateNode,dr(t,e),f=e.memoizedProps,N=Gl(l,f),i.props=N,z=e.pendingProps,A=i.context,T=l.contextType,y=fn,typeof T=="object"&&T!==null&&(y=Kt(T)),h=l.getDerivedStateFromProps,(T=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(f!==z||A!==y)&&Eo(e,i,n,y),il=!1,A=e.memoizedState,i.state=A,ra(e,n,i,a),ia();var R=e.memoizedState;f!==z||A!==R||il||t!==null&&t.dependencies!==null&&hu(t.dependencies)?(typeof h=="function"&&(Ur(e,l,h,n),R=e.memoizedState),(N=il||So(e,l,N,n,A,R,y)||t!==null&&t.dependencies!==null&&hu(t.dependencies))?(T||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(n,R,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(n,R,y)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),e.memoizedProps=n,e.memoizedState=R),i.props=n,i.state=R,i.context=y,n=N):(typeof i.componentDidUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),n=!1)}return i=n,wu(t,e),n=(e.flags&128)!==0,i||n?(i=e.stateNode,l=n&&typeof l.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&n?(e.child=Sn(e,t.child,null,a),e.child=Sn(e,null,l,a)):Xt(t,e,l,a),e.memoizedState=i.state,t=e.child):t=Ke(t,e,a),t}function Ho(t,e,l,n){return Pn(),e.flags|=256,Xt(t,e,l,n),e.child}var Lr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function jr(t){return{baseLanes:t,cachePool:_f()}}function Yr(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Se),t}function qo(t,e,l){var n=e.pendingProps,a=!1,i=(e.flags&128)!==0,f;if((f=i)||(f=t!==null&&t.memoizedState===null?!1:(Ht.current&2)!==0),f&&(a=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(ht){if(a?fl(e):ol(),ht){var h=Nt,y;if(y=h){t:{for(y=h,h=Ne;y.nodeType!==8;){if(!h){h=null;break t}if(y=Ae(y.nextSibling),y===null){h=null;break t}}h=y}h!==null?(e.memoizedState={dehydrated:h,treeContext:Bl!==null?{id:Xe,overflow:Ve}:null,retryLane:536870912,hydrationErrors:null},y=ie(18,null,null,0),y.stateNode=h,y.return=e,e.child=y,Wt=e,Nt=null,y=!0):y=!1}y||Ll(e)}if(h=e.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return Tc(h)?e.lanes=32:e.lanes=536870912,null;ke(e)}return h=n.children,n=n.fallback,a?(ol(),a=e.mode,h=zu({mode:"hidden",children:h},a),n=Ul(n,a,l,null),h.return=e,n.return=e,h.sibling=n,e.child=h,a=e.child,a.memoizedState=jr(l),a.childLanes=Yr(t,f,l),e.memoizedState=Lr,n):(fl(e),Xr(e,h))}if(y=t.memoizedState,y!==null&&(h=y.dehydrated,h!==null)){if(i)e.flags&256?(fl(e),e.flags&=-257,e=Vr(t,e,l)):e.memoizedState!==null?(ol(),e.child=t.child,e.flags|=128,e=null):(ol(),a=n.fallback,h=e.mode,n=zu({mode:"visible",children:n.children},h),a=Ul(a,h,l,null),a.flags|=2,n.return=e,a.return=e,n.sibling=a,e.child=n,Sn(e,t.child,null,l),n=e.child,n.memoizedState=jr(l),n.childLanes=Yr(t,f,l),e.memoizedState=Lr,e=a);else if(fl(e),Tc(h)){if(f=h.nextSibling&&h.nextSibling.dataset,f)var T=f.dgst;f=T,n=Error(s(419)),n.stack="",n.digest=f,In({value:n,source:null,stack:null}),e=Vr(t,e,l)}else if(Lt||ta(t,e,l,!1),f=(l&t.childLanes)!==0,Lt||f){if(f=Et,f!==null&&(n=l&-l,n=(n&42)!==0?1:xi(n),n=(n&(f.suspendedLanes|l))!==0?0:n,n!==0&&n!==y.retryLane))throw y.retryLane=n,sn(t,n),oe(f,t,n),No;h.data==="$?"||ic(),e=Vr(t,e,l)}else h.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=y.treeContext,Nt=Ae(h.nextSibling),Wt=e,ht=!0,ql=null,Ne=!1,t!==null&&(ve[ge++]=Xe,ve[ge++]=Ve,ve[ge++]=Bl,Xe=t.id,Ve=t.overflow,Bl=e),e=Xr(e,n.children),e.flags|=4096);return e}return a?(ol(),a=n.fallback,h=e.mode,y=t.child,T=y.sibling,n=Ye(y,{mode:"hidden",children:n.children}),n.subtreeFlags=y.subtreeFlags&65011712,T!==null?a=Ye(T,a):(a=Ul(a,h,l,null),a.flags|=2),a.return=e,n.return=e,n.sibling=a,e.child=n,n=a,a=e.child,h=t.child.memoizedState,h===null?h=jr(l):(y=h.cachePool,y!==null?(T=Bt._currentValue,y=y.parent!==T?{parent:T,pool:T}:y):y=_f(),h={baseLanes:h.baseLanes|l,cachePool:y}),a.memoizedState=h,a.childLanes=Yr(t,f,l),e.memoizedState=Lr,n):(fl(e),l=t.child,t=l.sibling,l=Ye(l,{mode:"visible",children:n.children}),l.return=e,l.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=l,e.memoizedState=null,l)}function Xr(t,e){return e=zu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function zu(t,e){return t=ie(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Vr(t,e,l){return Sn(e,t.child,null,l),t=Xr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Lo(t,e,l){t.lanes|=e;var n=t.alternate;n!==null&&(n.lanes|=e),ir(t.return,e,l)}function Gr(t,e,l,n,a){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:n,tail:l,tailMode:a}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=n,i.tail=l,i.tailMode=a)}function jo(t,e,l){var n=e.pendingProps,a=n.revealOrder,i=n.tail;if(Xt(t,e,n.children,l),n=Ht.current,(n&2)!==0)n=n&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Lo(t,l,e);else if(t.tag===19)Lo(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}n&=1}switch(j(Ht,n),a){case"forwards":for(l=e.child,a=null;l!==null;)t=l.alternate,t!==null&&Nu(t)===null&&(a=l),l=l.sibling;l=a,l===null?(a=e.child,e.child=null):(a=l.sibling,l.sibling=null),Gr(e,!1,a,l,i);break;case"backwards":for(l=null,a=e.child,e.child=null;a!==null;){if(t=a.alternate,t!==null&&Nu(t)===null){e.child=a;break}t=a.sibling,a.sibling=l,l=a,a=t}Gr(e,!0,l,null,i);break;case"together":Gr(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ke(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),pl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(ta(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,l=Ye(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Ye(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Qr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&hu(t)))}function Ny(t,e,l){switch(e.tag){case 3:xt(e,e.stateNode.containerInfo),ul(e,Bt,t.memoizedState.cache),Pn();break;case 27:case 5:bi(e);break;case 4:xt(e,e.stateNode.containerInfo);break;case 10:ul(e,e.type,e.memoizedProps.value);break;case 13:var n=e.memoizedState;if(n!==null)return n.dehydrated!==null?(fl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?qo(t,e,l):(fl(e),t=Ke(t,e,l),t!==null?t.sibling:null);fl(e);break;case 19:var a=(t.flags&128)!==0;if(n=(l&e.childLanes)!==0,n||(ta(t,e,l,!1),n=(l&e.childLanes)!==0),a){if(n)return jo(t,e,l);e.flags|=128}if(a=e.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),j(Ht,Ht.current),n)break;return null;case 22:case 23:return e.lanes=0,zo(t,e,l);case 24:ul(e,Bt,t.memoizedState.cache)}return Ke(t,e,l)}function Yo(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Lt=!0;else{if(!Qr(t,l)&&(e.flags&128)===0)return Lt=!1,Ny(t,e,l);Lt=(t.flags&131072)!==0}else Lt=!1,ht&&(e.flags&1048576)!==0&&yf(e,ou,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var n=e.elementType,a=n._init;if(n=a(n._payload),e.type=n,typeof n=="function")Ii(n)?(t=Gl(n,t),e.tag=1,e=Bo(null,e,n,t,l)):(e.tag=0,e=qr(null,e,n,t,l));else{if(n!=null){if(a=n.$$typeof,a===P){e.tag=11,e=Mo(null,e,n,t,l);break t}else if(a===Tt){e.tag=14,e=Do(null,e,n,t,l);break t}}throw e=Nl(n)||n,Error(s(306,e,""))}}return e;case 0:return qr(t,e,e.type,e.pendingProps,l);case 1:return n=e.type,a=Gl(n,e.pendingProps),Bo(t,e,n,a,l);case 3:t:{if(xt(e,e.stateNode.containerInfo),t===null)throw Error(s(387));n=e.pendingProps;var i=e.memoizedState;a=i.element,dr(t,e),ra(e,n,null,l);var f=e.memoizedState;if(n=f.cache,ul(e,Bt,n),n!==i.cache&&rr(e,[Bt],l,!0),ia(),n=f.element,i.isDehydrated)if(i={element:n,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=Ho(t,e,n,l);break t}else if(n!==a){a=ye(Error(s(424)),e),In(a),e=Ho(t,e,n,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Nt=Ae(t.firstChild),Wt=e,ht=!0,ql=null,Ne=!0,l=go(e,null,n,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Pn(),n===a){e=Ke(t,e,l);break t}Xt(t,e,n,l)}e=e.child}return e;case 26:return wu(t,e),t===null?(l=Qh(e.type,null,e.pendingProps,null))?e.memoizedState=l:ht||(l=e.type,t=e.pendingProps,n=ku(lt.current).createElement(l),n[kt]=e,n[Ft]=t,Gt(n,l,t),qt(n),e.stateNode=n):e.memoizedState=Qh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return bi(e),t===null&&ht&&(n=e.stateNode=Xh(e.type,e.pendingProps,lt.current),Wt=e,Ne=!0,a=Nt,Sl(e.type)?(xc=a,Nt=Ae(n.firstChild)):Nt=a),Xt(t,e,e.pendingProps.children,l),wu(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ht&&((a=n=Nt)&&(n=lp(n,e.type,e.pendingProps,Ne),n!==null?(e.stateNode=n,Wt=e,Nt=Ae(n.firstChild),Ne=!1,a=!0):a=!1),a||Ll(e)),bi(e),a=e.type,i=e.pendingProps,f=t!==null?t.memoizedProps:null,n=i.children,Sc(a,i)?n=null:f!==null&&Sc(a,f)&&(e.flags|=32),e.memoizedState!==null&&(a=br(t,e,Sy,null,null,l),Ma._currentValue=a),wu(t,e),Xt(t,e,n,l),e.child;case 6:return t===null&&ht&&((t=l=Nt)&&(l=np(l,e.pendingProps,Ne),l!==null?(e.stateNode=l,Wt=e,Nt=null,t=!0):t=!1),t||Ll(e)),null;case 13:return qo(t,e,l);case 4:return xt(e,e.stateNode.containerInfo),n=e.pendingProps,t===null?e.child=Sn(e,null,n,l):Xt(t,e,n,l),e.child;case 11:return Mo(t,e,e.type,e.pendingProps,l);case 7:return Xt(t,e,e.pendingProps,l),e.child;case 8:return Xt(t,e,e.pendingProps.children,l),e.child;case 12:return Xt(t,e,e.pendingProps.children,l),e.child;case 10:return n=e.pendingProps,ul(e,e.type,n.value),Xt(t,e,n.children,l),e.child;case 9:return a=e.type._context,n=e.pendingProps.children,Yl(e),a=Kt(a),n=n(a),e.flags|=1,Xt(t,e,n,l),e.child;case 14:return Do(t,e,e.type,e.pendingProps,l);case 15:return wo(t,e,e.type,e.pendingProps,l);case 19:return jo(t,e,l);case 31:return n=e.pendingProps,l=e.mode,n={mode:n.mode,children:n.children},t===null?(l=zu(n,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Ye(t.child,n),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return zo(t,e,l);case 24:return Yl(e),n=Kt(Bt),t===null?(a=fr(),a===null&&(a=Et,i=cr(),a.pooledCache=i,i.refCount++,i!==null&&(a.pooledCacheLanes|=l),a=i),e.memoizedState={parent:n,cache:a},hr(e),ul(e,Bt,a)):((t.lanes&l)!==0&&(dr(t,e),ra(e,null,null,l),ia()),a=t.memoizedState,i=e.memoizedState,a.parent!==n?(a={parent:n,cache:n},e.memoizedState=a,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=a),ul(e,Bt,n)):(n=i.cache,ul(e,Bt,n),n!==a.cache&&rr(e,[Bt],l,!0))),Xt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function Je(t){t.flags|=4}function Xo(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!$h(e)){if(e=be.current,e!==null&&((st&4194048)===st?Me!==null:(st&62914560)!==st&&(st&536870912)===0||e!==Me))throw aa=or,Tf;t.flags|=8192}}function Cu(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?vs():536870912,t.lanes|=e,xn|=e)}function ma(t,e){if(!ht)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var n=null;l!==null;)l.alternate!==null&&(n=l),l=l.sibling;n===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:n.sibling=null}}function Rt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,n=0;if(e)for(var a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags&65011712,n|=a.flags&65011712,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags,n|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=n,t.childLanes=l,e}function My(t,e,l){var n=e.pendingProps;switch(nr(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Rt(e),null;case 1:return Rt(e),null;case 3:return l=e.stateNode,n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Qe(Bt),el(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(Fn(e)?Je(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,gf())),Rt(e),null;case 26:return l=e.memoizedState,t===null?(Je(e),l!==null?(Rt(e),Xo(e,l)):(Rt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(Je(e),Rt(e),Xo(e,l)):(Rt(e),e.flags&=-16777217):(t.memoizedProps!==n&&Je(e),Rt(e),e.flags&=-16777217),null;case 27:Qa(e),l=lt.current;var a=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==n&&Je(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Rt(e),null}t=$.current,Fn(e)?pf(e):(t=Xh(a,n,l),e.stateNode=t,Je(e))}return Rt(e),null;case 5:if(Qa(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&Je(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Rt(e),null}if(t=$.current,Fn(e))pf(e);else{switch(a=ku(lt.current),t){case 1:t=a.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=a.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=a.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=a.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof n.is=="string"?a.createElement("select",{is:n.is}):a.createElement("select"),n.multiple?t.multiple=!0:n.size&&(t.size=n.size);break;default:t=typeof n.is=="string"?a.createElement(l,{is:n.is}):a.createElement(l)}}t[kt]=e,t[Ft]=n;t:for(a=e.child;a!==null;){if(a.tag===5||a.tag===6)t.appendChild(a.stateNode);else if(a.tag!==4&&a.tag!==27&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;a=a.return}a.sibling.return=a.return,a=a.sibling}e.stateNode=t;t:switch(Gt(t,l,n),l){case"button":case"input":case"select":case"textarea":t=!!n.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Je(e)}}return Rt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==n&&Je(e);else{if(typeof n!="string"&&e.stateNode===null)throw Error(s(166));if(t=lt.current,Fn(e)){if(t=e.stateNode,l=e.memoizedProps,n=null,a=Wt,a!==null)switch(a.tag){case 27:case 5:n=a.memoizedProps}t[kt]=e,t=!!(t.nodeValue===l||n!==null&&n.suppressHydrationWarning===!0||Uh(t.nodeValue,l)),t||Ll(e)}else t=ku(t).createTextNode(n),t[kt]=e,e.stateNode=t}return Rt(e),null;case 13:if(n=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(a=Fn(e),n!==null&&n.dehydrated!==null){if(t===null){if(!a)throw Error(s(318));if(a=e.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(s(317));a[kt]=e}else Pn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Rt(e),a=!1}else a=gf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=a),a=!0;if(!a)return e.flags&256?(ke(e),e):(ke(e),null)}if(ke(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=n!==null,t=t!==null&&t.memoizedState!==null,l){n=e.child,a=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(a=n.alternate.memoizedState.cachePool.pool);var i=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(i=n.memoizedState.cachePool.pool),i!==a&&(n.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Cu(e,e.updateQueue),Rt(e),null;case 4:return el(),t===null&&yc(e.stateNode.containerInfo),Rt(e),null;case 10:return Qe(e.type),Rt(e),null;case 19:if(G(Ht),a=e.memoizedState,a===null)return Rt(e),null;if(n=(e.flags&128)!==0,i=a.rendering,i===null)if(n)ma(a,!1);else{if(Mt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=Nu(t),i!==null){for(e.flags|=128,ma(a,!1),t=i.updateQueue,e.updateQueue=t,Cu(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)mf(l,t),l=l.sibling;return j(Ht,Ht.current&1|2),e.child}t=t.sibling}a.tail!==null&&Oe()>Hu&&(e.flags|=128,n=!0,ma(a,!1),e.lanes=4194304)}else{if(!n)if(t=Nu(i),t!==null){if(e.flags|=128,n=!0,t=t.updateQueue,e.updateQueue=t,Cu(e,t),ma(a,!0),a.tail===null&&a.tailMode==="hidden"&&!i.alternate&&!ht)return Rt(e),null}else 2*Oe()-a.renderingStartTime>Hu&&l!==536870912&&(e.flags|=128,n=!0,ma(a,!1),e.lanes=4194304);a.isBackwards?(i.sibling=e.child,e.child=i):(t=a.last,t!==null?t.sibling=i:e.child=i,a.last=i)}return a.tail!==null?(e=a.tail,a.rendering=e,a.tail=e.sibling,a.renderingStartTime=Oe(),e.sibling=null,t=Ht.current,j(Ht,n?t&1|2:t&1),e):(Rt(e),null);case 22:case 23:return ke(e),vr(),n=e.memoizedState!==null,t!==null?t.memoizedState!==null!==n&&(e.flags|=8192):n&&(e.flags|=8192),n?(l&536870912)!==0&&(e.flags&128)===0&&(Rt(e),e.subtreeFlags&6&&(e.flags|=8192)):Rt(e),l=e.updateQueue,l!==null&&Cu(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),n=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),n!==l&&(e.flags|=2048),t!==null&&G(Xl),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Qe(Bt),Rt(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function Dy(t,e){switch(nr(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Qe(Bt),el(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Qa(e),null;case 13:if(ke(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));Pn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return G(Ht),null;case 4:return el(),null;case 10:return Qe(e.type),null;case 22:case 23:return ke(e),vr(),t!==null&&G(Xl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Qe(Bt),null;case 25:return null;default:return null}}function Vo(t,e){switch(nr(e),e.tag){case 3:Qe(Bt),el();break;case 26:case 27:case 5:Qa(e);break;case 4:el();break;case 13:ke(e);break;case 19:G(Ht);break;case 10:Qe(e.type);break;case 22:case 23:ke(e),vr(),t!==null&&G(Xl);break;case 24:Qe(Bt)}}function ya(t,e){try{var l=e.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var a=n.next;l=a;do{if((l.tag&t)===t){n=void 0;var i=l.create,f=l.inst;n=i(),f.destroy=n}l=l.next}while(l!==a)}}catch(h){St(e,e.return,h)}}function hl(t,e,l){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var i=a.next;n=i;do{if((n.tag&t)===t){var f=n.inst,h=f.destroy;if(h!==void 0){f.destroy=void 0,a=e;var y=l,T=h;try{T()}catch(N){St(a,y,N)}}}n=n.next}while(n!==i)}}catch(N){St(e,e.return,N)}}function Go(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{Mf(e,l)}catch(n){St(t,t.return,n)}}}function Qo(t,e,l){l.props=Gl(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(n){St(t,e,n)}}function pa(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var n=t.stateNode;break;case 30:n=t.stateNode;break;default:n=t.stateNode}typeof l=="function"?t.refCleanup=l(n):l.current=n}}catch(a){St(t,e,a)}}function De(t,e){var l=t.ref,n=t.refCleanup;if(l!==null)if(typeof n=="function")try{n()}catch(a){St(t,e,a)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(a){St(t,e,a)}else l.current=null}function Zo(t){var e=t.type,l=t.memoizedProps,n=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break t;case"img":l.src?n.src=l.src:l.srcSet&&(n.srcset=l.srcSet)}}catch(a){St(t,t.return,a)}}function Zr(t,e,l){try{var n=t.stateNode;Fy(n,t.type,l,e),n[Ft]=e}catch(a){St(t,t.return,a)}}function ko(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Sl(t.type)||t.tag===4}function kr(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||ko(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Sl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Kr(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Zu));else if(n!==4&&(n===27&&Sl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Kr(t,e,l),t=t.sibling;t!==null;)Kr(t,e,l),t=t.sibling}function Uu(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(n!==4&&(n===27&&Sl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Uu(t,e,l),t=t.sibling;t!==null;)Uu(t,e,l),t=t.sibling}function Ko(t){var e=t.stateNode,l=t.memoizedProps;try{for(var n=t.type,a=e.attributes;a.length;)e.removeAttributeNode(a[0]);Gt(e,n,l),e[kt]=t,e[Ft]=l}catch(i){St(t,t.return,i)}}var $e=!1,wt=!1,Jr=!1,Jo=typeof WeakSet=="function"?WeakSet:Set,jt=null;function wy(t,e){if(t=t.containerInfo,gc=Pu,t=nf(t),ki(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var n=l.getSelection&&l.getSelection();if(n&&n.rangeCount!==0){l=n.anchorNode;var a=n.anchorOffset,i=n.focusNode;n=n.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break t}var f=0,h=-1,y=-1,T=0,N=0,z=t,A=null;e:for(;;){for(var R;z!==l||a!==0&&z.nodeType!==3||(h=f+a),z!==i||n!==0&&z.nodeType!==3||(y=f+n),z.nodeType===3&&(f+=z.nodeValue.length),(R=z.firstChild)!==null;)A=z,z=R;for(;;){if(z===t)break e;if(A===l&&++T===a&&(h=f),A===i&&++N===n&&(y=f),(R=z.nextSibling)!==null)break;z=A,A=z.parentNode}z=R}l=h===-1||y===-1?null:{start:h,end:y}}else l=null}l=l||{start:0,end:0}}else l=null;for(bc={focusedElem:t,selectionRange:l},Pu=!1,jt=e;jt!==null;)if(e=jt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,jt=t;else for(;jt!==null;){switch(e=jt,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,l=e,a=i.memoizedProps,i=i.memoizedState,n=l.stateNode;try{var tt=Gl(l.type,a,l.elementType===l.type);t=n.getSnapshotBeforeUpdate(tt,i),n.__reactInternalSnapshotBeforeUpdate=t}catch(W){St(l,l.return,W)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)_c(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":_c(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,jt=t;break}jt=e.return}}function $o(t,e,l){var n=l.flags;switch(l.tag){case 0:case 11:case 15:dl(t,l),n&4&&ya(5,l);break;case 1:if(dl(t,l),n&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(f){St(l,l.return,f)}else{var a=Gl(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(a,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){St(l,l.return,f)}}n&64&&Go(l),n&512&&pa(l,l.return);break;case 3:if(dl(t,l),n&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{Mf(t,e)}catch(f){St(l,l.return,f)}}break;case 27:e===null&&n&4&&Ko(l);case 26:case 5:dl(t,l),e===null&&n&4&&Zo(l),n&512&&pa(l,l.return);break;case 12:dl(t,l);break;case 13:dl(t,l),n&4&&Po(t,l),n&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Yy.bind(null,l),ap(t,l))));break;case 22:if(n=l.memoizedState!==null||$e,!n){e=e!==null&&e.memoizedState!==null||wt,a=$e;var i=wt;$e=n,(wt=e)&&!i?ml(t,l,(l.subtreeFlags&8772)!==0):dl(t,l),$e=a,wt=i}break;case 30:break;default:dl(t,l)}}function Wo(t){var e=t.alternate;e!==null&&(t.alternate=null,Wo(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Oi(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var At=null,te=!1;function We(t,e,l){for(l=l.child;l!==null;)Fo(t,e,l),l=l.sibling}function Fo(t,e,l){if(ne&&typeof ne.onCommitFiberUnmount=="function")try{ne.onCommitFiberUnmount(qn,l)}catch{}switch(l.tag){case 26:wt||De(l,e),We(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:wt||De(l,e);var n=At,a=te;Sl(l.type)&&(At=l.stateNode,te=!1),We(t,e,l),Aa(l.stateNode),At=n,te=a;break;case 5:wt||De(l,e);case 6:if(n=At,a=te,At=null,We(t,e,l),At=n,te=a,At!==null)if(te)try{(At.nodeType===9?At.body:At.nodeName==="HTML"?At.ownerDocument.body:At).removeChild(l.stateNode)}catch(i){St(l,e,i)}else try{At.removeChild(l.stateNode)}catch(i){St(l,e,i)}break;case 18:At!==null&&(te?(t=At,jh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Ca(t)):jh(At,l.stateNode));break;case 4:n=At,a=te,At=l.stateNode.containerInfo,te=!0,We(t,e,l),At=n,te=a;break;case 0:case 11:case 14:case 15:wt||hl(2,l,e),wt||hl(4,l,e),We(t,e,l);break;case 1:wt||(De(l,e),n=l.stateNode,typeof n.componentWillUnmount=="function"&&Qo(l,e,n)),We(t,e,l);break;case 21:We(t,e,l);break;case 22:wt=(n=wt)||l.memoizedState!==null,We(t,e,l),wt=n;break;default:We(t,e,l)}}function Po(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ca(t)}catch(l){St(e,e.return,l)}}function zy(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Jo),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Jo),e;default:throw Error(s(435,t.tag))}}function $r(t,e){var l=zy(t);e.forEach(function(n){var a=Xy.bind(null,t,n);l.has(n)||(l.add(n),n.then(a,a))})}function re(t,e){var l=e.deletions;if(l!==null)for(var n=0;n<l.length;n++){var a=l[n],i=t,f=e,h=f;t:for(;h!==null;){switch(h.tag){case 27:if(Sl(h.type)){At=h.stateNode,te=!1;break t}break;case 5:At=h.stateNode,te=!1;break t;case 3:case 4:At=h.stateNode.containerInfo,te=!0;break t}h=h.return}if(At===null)throw Error(s(160));Fo(i,f,a),At=null,te=!1,i=a.alternate,i!==null&&(i.return=null),a.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Io(e,t),e=e.sibling}var xe=null;function Io(t,e){var l=t.alternate,n=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:re(e,t),ce(t),n&4&&(hl(3,t,t.return),ya(3,t),hl(5,t,t.return));break;case 1:re(e,t),ce(t),n&512&&(wt||l===null||De(l,l.return)),n&64&&$e&&(t=t.updateQueue,t!==null&&(n=t.callbacks,n!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?n:l.concat(n))));break;case 26:var a=xe;if(re(e,t),ce(t),n&512&&(wt||l===null||De(l,l.return)),n&4){var i=l!==null?l.memoizedState:null;if(n=t.memoizedState,l===null)if(n===null)if(t.stateNode===null){t:{n=t.type,l=t.memoizedProps,a=a.ownerDocument||a;e:switch(n){case"title":i=a.getElementsByTagName("title")[0],(!i||i[Yn]||i[kt]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=a.createElement(n),a.head.insertBefore(i,a.querySelector("head > title"))),Gt(i,n,l),i[kt]=t,qt(i),n=i;break t;case"link":var f=Kh("link","href",a).get(n+(l.href||""));if(f){for(var h=0;h<f.length;h++)if(i=f[h],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){f.splice(h,1);break e}}i=a.createElement(n),Gt(i,n,l),a.head.appendChild(i);break;case"meta":if(f=Kh("meta","content",a).get(n+(l.content||""))){for(h=0;h<f.length;h++)if(i=f[h],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){f.splice(h,1);break e}}i=a.createElement(n),Gt(i,n,l),a.head.appendChild(i);break;default:throw Error(s(468,n))}i[kt]=t,qt(i),n=i}t.stateNode=n}else Jh(a,t.type,t.stateNode);else t.stateNode=kh(a,n,t.memoizedProps);else i!==n?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,n===null?Jh(a,t.type,t.stateNode):kh(a,n,t.memoizedProps)):n===null&&t.stateNode!==null&&Zr(t,t.memoizedProps,l.memoizedProps)}break;case 27:re(e,t),ce(t),n&512&&(wt||l===null||De(l,l.return)),l!==null&&n&4&&Zr(t,t.memoizedProps,l.memoizedProps);break;case 5:if(re(e,t),ce(t),n&512&&(wt||l===null||De(l,l.return)),t.flags&32){a=t.stateNode;try{en(a,"")}catch(R){St(t,t.return,R)}}n&4&&t.stateNode!=null&&(a=t.memoizedProps,Zr(t,a,l!==null?l.memoizedProps:a)),n&1024&&(Jr=!0);break;case 6:if(re(e,t),ce(t),n&4){if(t.stateNode===null)throw Error(s(162));n=t.memoizedProps,l=t.stateNode;try{l.nodeValue=n}catch(R){St(t,t.return,R)}}break;case 3:if($u=null,a=xe,xe=Ku(e.containerInfo),re(e,t),xe=a,ce(t),n&4&&l!==null&&l.memoizedState.isDehydrated)try{Ca(e.containerInfo)}catch(R){St(t,t.return,R)}Jr&&(Jr=!1,th(t));break;case 4:n=xe,xe=Ku(t.stateNode.containerInfo),re(e,t),ce(t),xe=n;break;case 12:re(e,t),ce(t);break;case 13:re(e,t),ce(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(ec=Oe()),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,$r(t,n)));break;case 22:a=t.memoizedState!==null;var y=l!==null&&l.memoizedState!==null,T=$e,N=wt;if($e=T||a,wt=N||y,re(e,t),wt=N,$e=T,ce(t),n&8192)t:for(e=t.stateNode,e._visibility=a?e._visibility&-2:e._visibility|1,a&&(l===null||y||$e||wt||Ql(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){y=l=e;try{if(i=y.stateNode,a)f=i.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{h=y.stateNode;var z=y.memoizedProps.style,A=z!=null&&z.hasOwnProperty("display")?z.display:null;h.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(R){St(y,y.return,R)}}}else if(e.tag===6){if(l===null){y=e;try{y.stateNode.nodeValue=a?"":y.memoizedProps}catch(R){St(y,y.return,R)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}n&4&&(n=t.updateQueue,n!==null&&(l=n.retryQueue,l!==null&&(n.retryQueue=null,$r(t,l))));break;case 19:re(e,t),ce(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,$r(t,n)));break;case 30:break;case 21:break;default:re(e,t),ce(t)}}function ce(t){var e=t.flags;if(e&2){try{for(var l,n=t.return;n!==null;){if(ko(n)){l=n;break}n=n.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var a=l.stateNode,i=kr(t);Uu(t,i,a);break;case 5:var f=l.stateNode;l.flags&32&&(en(f,""),l.flags&=-33);var h=kr(t);Uu(t,h,f);break;case 3:case 4:var y=l.stateNode.containerInfo,T=kr(t);Kr(t,T,y);break;default:throw Error(s(161))}}catch(N){St(t,t.return,N)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function th(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;th(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function dl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)$o(t,e.alternate,e),e=e.sibling}function Ql(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:hl(4,e,e.return),Ql(e);break;case 1:De(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Qo(e,e.return,l),Ql(e);break;case 27:Aa(e.stateNode);case 26:case 5:De(e,e.return),Ql(e);break;case 22:e.memoizedState===null&&Ql(e);break;case 30:Ql(e);break;default:Ql(e)}t=t.sibling}}function ml(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var n=e.alternate,a=t,i=e,f=i.flags;switch(i.tag){case 0:case 11:case 15:ml(a,i,l),ya(4,i);break;case 1:if(ml(a,i,l),n=i,a=n.stateNode,typeof a.componentDidMount=="function")try{a.componentDidMount()}catch(T){St(n,n.return,T)}if(n=i,a=n.updateQueue,a!==null){var h=n.stateNode;try{var y=a.shared.hiddenCallbacks;if(y!==null)for(a.shared.hiddenCallbacks=null,a=0;a<y.length;a++)Nf(y[a],h)}catch(T){St(n,n.return,T)}}l&&f&64&&Go(i),pa(i,i.return);break;case 27:Ko(i);case 26:case 5:ml(a,i,l),l&&n===null&&f&4&&Zo(i),pa(i,i.return);break;case 12:ml(a,i,l);break;case 13:ml(a,i,l),l&&f&4&&Po(a,i);break;case 22:i.memoizedState===null&&ml(a,i,l),pa(i,i.return);break;case 30:break;default:ml(a,i,l)}e=e.sibling}}function Wr(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&ea(l))}function Fr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ea(t))}function we(t,e,l,n){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)eh(t,e,l,n),e=e.sibling}function eh(t,e,l,n){var a=e.flags;switch(e.tag){case 0:case 11:case 15:we(t,e,l,n),a&2048&&ya(9,e);break;case 1:we(t,e,l,n);break;case 3:we(t,e,l,n),a&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ea(t)));break;case 12:if(a&2048){we(t,e,l,n),t=e.stateNode;try{var i=e.memoizedProps,f=i.id,h=i.onPostCommit;typeof h=="function"&&h(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(y){St(e,e.return,y)}}else we(t,e,l,n);break;case 13:we(t,e,l,n);break;case 23:break;case 22:i=e.stateNode,f=e.alternate,e.memoizedState!==null?i._visibility&2?we(t,e,l,n):va(t,e):i._visibility&2?we(t,e,l,n):(i._visibility|=2,En(t,e,l,n,(e.subtreeFlags&10256)!==0)),a&2048&&Wr(f,e);break;case 24:we(t,e,l,n),a&2048&&Fr(e.alternate,e);break;default:we(t,e,l,n)}}function En(t,e,l,n,a){for(a=a&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,f=e,h=l,y=n,T=f.flags;switch(f.tag){case 0:case 11:case 15:En(i,f,h,y,a),ya(8,f);break;case 23:break;case 22:var N=f.stateNode;f.memoizedState!==null?N._visibility&2?En(i,f,h,y,a):va(i,f):(N._visibility|=2,En(i,f,h,y,a)),a&&T&2048&&Wr(f.alternate,f);break;case 24:En(i,f,h,y,a),a&&T&2048&&Fr(f.alternate,f);break;default:En(i,f,h,y,a)}e=e.sibling}}function va(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,n=e,a=n.flags;switch(n.tag){case 22:va(l,n),a&2048&&Wr(n.alternate,n);break;case 24:va(l,n),a&2048&&Fr(n.alternate,n);break;default:va(l,n)}e=e.sibling}}var ga=8192;function _n(t){if(t.subtreeFlags&ga)for(t=t.child;t!==null;)lh(t),t=t.sibling}function lh(t){switch(t.tag){case 26:_n(t),t.flags&ga&&t.memoizedState!==null&&vp(xe,t.memoizedState,t.memoizedProps);break;case 5:_n(t);break;case 3:case 4:var e=xe;xe=Ku(t.stateNode.containerInfo),_n(t),xe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ga,ga=16777216,_n(t),ga=e):_n(t));break;default:_n(t)}}function nh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ba(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];jt=n,uh(n,t)}nh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ah(t),t=t.sibling}function ah(t){switch(t.tag){case 0:case 11:case 15:ba(t),t.flags&2048&&hl(9,t,t.return);break;case 3:ba(t);break;case 12:ba(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Bu(t)):ba(t);break;default:ba(t)}}function Bu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];jt=n,uh(n,t)}nh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:hl(8,e,e.return),Bu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Bu(e));break;default:Bu(e)}t=t.sibling}}function uh(t,e){for(;jt!==null;){var l=jt;switch(l.tag){case 0:case 11:case 15:hl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var n=l.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:ea(l.memoizedState.cache)}if(n=l.child,n!==null)n.return=l,jt=n;else t:for(l=t;jt!==null;){n=jt;var a=n.sibling,i=n.return;if(Wo(n),n===l){jt=null;break t}if(a!==null){a.return=i,jt=a;break t}jt=i}}}var Cy={getCacheForType:function(t){var e=Kt(Bt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},Uy=typeof WeakMap=="function"?WeakMap:Map,mt=0,Et=null,it=null,st=0,yt=0,se=null,yl=!1,Tn=!1,Pr=!1,Fe=0,Mt=0,pl=0,Zl=0,Ir=0,Se=0,xn=0,Sa=null,ee=null,tc=!1,ec=0,Hu=1/0,qu=null,vl=null,Vt=0,gl=null,An=null,Rn=0,lc=0,nc=null,ih=null,Ea=0,ac=null;function fe(){if((mt&2)!==0&&st!==0)return st&-st;if(M.T!==null){var t=dn;return t!==0?t:oc()}return Ss()}function rh(){Se===0&&(Se=(st&536870912)===0||ht?ps():536870912);var t=be.current;return t!==null&&(t.flags|=32),Se}function oe(t,e,l){(t===Et&&(yt===2||yt===9)||t.cancelPendingCommit!==null)&&(On(t,0),bl(t,st,Se,!1)),jn(t,l),((mt&2)===0||t!==Et)&&(t===Et&&((mt&2)===0&&(Zl|=l),Mt===4&&bl(t,st,Se,!1)),ze(t))}function ch(t,e,l){if((mt&6)!==0)throw Error(s(327));var n=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Ln(t,e),a=n?qy(t,e):rc(t,e,!0),i=n;do{if(a===0){Tn&&!n&&bl(t,e,0,!1);break}else{if(l=t.current.alternate,i&&!By(l)){a=rc(t,e,!1),i=!1;continue}if(a===2){if(i=e,t.errorRecoveryDisabledLanes&i)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var h=t;a=Sa;var y=h.current.memoizedState.isDehydrated;if(y&&(On(h,f).flags|=256),f=rc(h,f,!1),f!==2){if(Pr&&!y){h.errorRecoveryDisabledLanes|=i,Zl|=i,a=4;break t}i=ee,ee=a,i!==null&&(ee===null?ee=i:ee.push.apply(ee,i))}a=f}if(i=!1,a!==2)continue}}if(a===1){On(t,0),bl(t,e,0,!0);break}t:{switch(n=t,i=a,i){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:bl(n,e,Se,!yl);break t;case 2:ee=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(a=ec+300-Oe(),10<a)){if(bl(n,e,Se,!yl),Ja(n,0,!0)!==0)break t;n.timeoutHandle=qh(sh.bind(null,n,l,ee,qu,tc,e,Se,Zl,xn,yl,i,2,-0,0),a);break t}sh(n,l,ee,qu,tc,e,Se,Zl,xn,yl,i,0,-0,0)}}break}while(!0);ze(t)}function sh(t,e,l,n,a,i,f,h,y,T,N,z,A,R){if(t.timeoutHandle=-1,z=e.subtreeFlags,(z&8192||(z&16785408)===16785408)&&(Na={stylesheets:null,count:0,unsuspend:pp},lh(e),z=gp(),z!==null)){t.cancelPendingCommit=z(ph.bind(null,t,e,i,l,n,a,f,h,y,N,1,A,R)),bl(t,i,f,!T);return}ph(t,e,i,l,n,a,f,h,y)}function By(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var n=0;n<l.length;n++){var a=l[n],i=a.getSnapshot;a=a.value;try{if(!ue(i(),a))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function bl(t,e,l,n){e&=~Ir,e&=~Zl,t.suspendedLanes|=e,t.pingedLanes&=~e,n&&(t.warmLanes|=e),n=t.expirationTimes;for(var a=e;0<a;){var i=31-ae(a),f=1<<i;n[i]=-1,a&=~f}l!==0&&gs(t,l,e)}function Lu(){return(mt&6)===0?(_a(0),!1):!0}function uc(){if(it!==null){if(yt===0)var t=it.return;else t=it,Ge=jl=null,_r(t),bn=null,ha=0,t=it;for(;t!==null;)Vo(t.alternate,t),t=t.return;it=null}}function On(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Iy(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),uc(),Et=t,it=l=Ye(t.current,null),st=e,yt=0,se=null,yl=!1,Tn=Ln(t,e),Pr=!1,xn=Se=Ir=Zl=pl=Mt=0,ee=Sa=null,tc=!1,(e&8)!==0&&(e|=e&32);var n=t.entangledLanes;if(n!==0)for(t=t.entanglements,n&=e;0<n;){var a=31-ae(n),i=1<<a;e|=t[a],n&=~i}return Fe=e,iu(),l}function fh(t,e){at=null,M.H=Au,e===na||e===yu?(e=Rf(),yt=3):e===Tf?(e=Rf(),yt=4):yt=e===No?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,se=e,it===null&&(Mt=1,Du(t,ye(e,t.current)))}function oh(){var t=M.H;return M.H=Au,t===null?Au:t}function hh(){var t=M.A;return M.A=Cy,t}function ic(){Mt=4,yl||(st&4194048)!==st&&be.current!==null||(Tn=!0),(pl&134217727)===0&&(Zl&134217727)===0||Et===null||bl(Et,st,Se,!1)}function rc(t,e,l){var n=mt;mt|=2;var a=oh(),i=hh();(Et!==t||st!==e)&&(qu=null,On(t,e)),e=!1;var f=Mt;t:do try{if(yt!==0&&it!==null){var h=it,y=se;switch(yt){case 8:uc(),f=6;break t;case 3:case 2:case 9:case 6:be.current===null&&(e=!0);var T=yt;if(yt=0,se=null,Nn(t,h,y,T),l&&Tn){f=0;break t}break;default:T=yt,yt=0,se=null,Nn(t,h,y,T)}}Hy(),f=Mt;break}catch(N){fh(t,N)}while(!0);return e&&t.shellSuspendCounter++,Ge=jl=null,mt=n,M.H=a,M.A=i,it===null&&(Et=null,st=0,iu()),f}function Hy(){for(;it!==null;)dh(it)}function qy(t,e){var l=mt;mt|=2;var n=oh(),a=hh();Et!==t||st!==e?(qu=null,Hu=Oe()+500,On(t,e)):Tn=Ln(t,e);t:do try{if(yt!==0&&it!==null){e=it;var i=se;e:switch(yt){case 1:yt=0,se=null,Nn(t,e,i,1);break;case 2:case 9:if(xf(i)){yt=0,se=null,mh(e);break}e=function(){yt!==2&&yt!==9||Et!==t||(yt=7),ze(t)},i.then(e,e);break t;case 3:yt=7;break t;case 4:yt=5;break t;case 7:xf(i)?(yt=0,se=null,mh(e)):(yt=0,se=null,Nn(t,e,i,7));break;case 5:var f=null;switch(it.tag){case 26:f=it.memoizedState;case 5:case 27:var h=it;if(!f||$h(f)){yt=0,se=null;var y=h.sibling;if(y!==null)it=y;else{var T=h.return;T!==null?(it=T,ju(T)):it=null}break e}}yt=0,se=null,Nn(t,e,i,5);break;case 6:yt=0,se=null,Nn(t,e,i,6);break;case 8:uc(),Mt=6;break t;default:throw Error(s(462))}}Ly();break}catch(N){fh(t,N)}while(!0);return Ge=jl=null,M.H=n,M.A=a,mt=l,it!==null?0:(Et=null,st=0,iu(),Mt)}function Ly(){for(;it!==null&&!im();)dh(it)}function dh(t){var e=Yo(t.alternate,t,Fe);t.memoizedProps=t.pendingProps,e===null?ju(t):it=e}function mh(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=Uo(l,e,e.pendingProps,e.type,void 0,st);break;case 11:e=Uo(l,e,e.pendingProps,e.type.render,e.ref,st);break;case 5:_r(e);default:Vo(l,e),e=it=mf(e,Fe),e=Yo(l,e,Fe)}t.memoizedProps=t.pendingProps,e===null?ju(t):it=e}function Nn(t,e,l,n){Ge=jl=null,_r(e),bn=null,ha=0;var a=e.return;try{if(Oy(t,a,e,l,st)){Mt=1,Du(t,ye(l,t.current)),it=null;return}}catch(i){if(a!==null)throw it=a,i;Mt=1,Du(t,ye(l,t.current)),it=null;return}e.flags&32768?(ht||n===1?t=!0:Tn||(st&536870912)!==0?t=!1:(yl=t=!0,(n===2||n===9||n===3||n===6)&&(n=be.current,n!==null&&n.tag===13&&(n.flags|=16384))),yh(e,t)):ju(e)}function ju(t){var e=t;do{if((e.flags&32768)!==0){yh(e,yl);return}t=e.return;var l=My(e.alternate,e,Fe);if(l!==null){it=l;return}if(e=e.sibling,e!==null){it=e;return}it=e=t}while(e!==null);Mt===0&&(Mt=5)}function yh(t,e){do{var l=Dy(t.alternate,t);if(l!==null){l.flags&=32767,it=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){it=t;return}it=t=l}while(t!==null);Mt=6,it=null}function ph(t,e,l,n,a,i,f,h,y){t.cancelPendingCommit=null;do Yu();while(Vt!==0);if((mt&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(i=e.lanes|e.childLanes,i|=Fi,pm(t,l,i,f,h,y),t===Et&&(it=Et=null,st=0),An=e,gl=t,Rn=l,lc=i,nc=a,ih=n,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Vy(Za,function(){return Eh(),null})):(t.callbackNode=null,t.callbackPriority=0),n=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||n){n=M.T,M.T=null,a=X.p,X.p=2,f=mt,mt|=4;try{wy(t,e,l)}finally{mt=f,X.p=a,M.T=n}}Vt=1,vh(),gh(),bh()}}function vh(){if(Vt===1){Vt=0;var t=gl,e=An,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=M.T,M.T=null;var n=X.p;X.p=2;var a=mt;mt|=4;try{Io(e,t);var i=bc,f=nf(t.containerInfo),h=i.focusedElem,y=i.selectionRange;if(f!==h&&h&&h.ownerDocument&&lf(h.ownerDocument.documentElement,h)){if(y!==null&&ki(h)){var T=y.start,N=y.end;if(N===void 0&&(N=T),"selectionStart"in h)h.selectionStart=T,h.selectionEnd=Math.min(N,h.value.length);else{var z=h.ownerDocument||document,A=z&&z.defaultView||window;if(A.getSelection){var R=A.getSelection(),tt=h.textContent.length,W=Math.min(y.start,tt),gt=y.end===void 0?W:Math.min(y.end,tt);!R.extend&&W>gt&&(f=gt,gt=W,W=f);var S=ef(h,W),g=ef(h,gt);if(S&&g&&(R.rangeCount!==1||R.anchorNode!==S.node||R.anchorOffset!==S.offset||R.focusNode!==g.node||R.focusOffset!==g.offset)){var _=z.createRange();_.setStart(S.node,S.offset),R.removeAllRanges(),W>gt?(R.addRange(_),R.extend(g.node,g.offset)):(_.setEnd(g.node,g.offset),R.addRange(_))}}}}for(z=[],R=h;R=R.parentNode;)R.nodeType===1&&z.push({element:R,left:R.scrollLeft,top:R.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<z.length;h++){var w=z[h];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}Pu=!!gc,bc=gc=null}finally{mt=a,X.p=n,M.T=l}}t.current=e,Vt=2}}function gh(){if(Vt===2){Vt=0;var t=gl,e=An,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=M.T,M.T=null;var n=X.p;X.p=2;var a=mt;mt|=4;try{$o(t,e.alternate,e)}finally{mt=a,X.p=n,M.T=l}}Vt=3}}function bh(){if(Vt===4||Vt===3){Vt=0,rm();var t=gl,e=An,l=Rn,n=ih;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Vt=5:(Vt=0,An=gl=null,Sh(t,t.pendingLanes));var a=t.pendingLanes;if(a===0&&(vl=null),Ai(l),e=e.stateNode,ne&&typeof ne.onCommitFiberRoot=="function")try{ne.onCommitFiberRoot(qn,e,void 0,(e.current.flags&128)===128)}catch{}if(n!==null){e=M.T,a=X.p,X.p=2,M.T=null;try{for(var i=t.onRecoverableError,f=0;f<n.length;f++){var h=n[f];i(h.value,{componentStack:h.stack})}}finally{M.T=e,X.p=a}}(Rn&3)!==0&&Yu(),ze(t),a=t.pendingLanes,(l&4194090)!==0&&(a&42)!==0?t===ac?Ea++:(Ea=0,ac=t):Ea=0,_a(0)}}function Sh(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,ea(e)))}function Yu(t){return vh(),gh(),bh(),Eh()}function Eh(){if(Vt!==5)return!1;var t=gl,e=lc;lc=0;var l=Ai(Rn),n=M.T,a=X.p;try{X.p=32>l?32:l,M.T=null,l=nc,nc=null;var i=gl,f=Rn;if(Vt=0,An=gl=null,Rn=0,(mt&6)!==0)throw Error(s(331));var h=mt;if(mt|=4,ah(i.current),eh(i,i.current,f,l),mt=h,_a(0,!1),ne&&typeof ne.onPostCommitFiberRoot=="function")try{ne.onPostCommitFiberRoot(qn,i)}catch{}return!0}finally{X.p=a,M.T=n,Sh(t,e)}}function _h(t,e,l){e=ye(l,e),e=Hr(t.stateNode,e,2),t=cl(t,e,2),t!==null&&(jn(t,2),ze(t))}function St(t,e,l){if(t.tag===3)_h(t,t,l);else for(;e!==null;){if(e.tag===3){_h(e,t,l);break}else if(e.tag===1){var n=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(vl===null||!vl.has(n))){t=ye(l,t),l=Ro(2),n=cl(e,l,2),n!==null&&(Oo(l,n,e,t),jn(n,2),ze(n));break}}e=e.return}}function cc(t,e,l){var n=t.pingCache;if(n===null){n=t.pingCache=new Uy;var a=new Set;n.set(e,a)}else a=n.get(e),a===void 0&&(a=new Set,n.set(e,a));a.has(l)||(Pr=!0,a.add(l),t=jy.bind(null,t,e,l),e.then(t,t))}function jy(t,e,l){var n=t.pingCache;n!==null&&n.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Et===t&&(st&l)===l&&(Mt===4||Mt===3&&(st&62914560)===st&&300>Oe()-ec?(mt&2)===0&&On(t,0):Ir|=l,xn===st&&(xn=0)),ze(t)}function Th(t,e){e===0&&(e=vs()),t=sn(t,e),t!==null&&(jn(t,e),ze(t))}function Yy(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),Th(t,l)}function Xy(t,e){var l=0;switch(t.tag){case 13:var n=t.stateNode,a=t.memoizedState;a!==null&&(l=a.retryLane);break;case 19:n=t.stateNode;break;case 22:n=t.stateNode._retryCache;break;default:throw Error(s(314))}n!==null&&n.delete(e),Th(t,l)}function Vy(t,e){return Ei(t,e)}var Xu=null,Mn=null,sc=!1,Vu=!1,fc=!1,kl=0;function ze(t){t!==Mn&&t.next===null&&(Mn===null?Xu=Mn=t:Mn=Mn.next=t),Vu=!0,sc||(sc=!0,Qy())}function _a(t,e){if(!fc&&Vu){fc=!0;do for(var l=!1,n=Xu;n!==null;){if(t!==0){var a=n.pendingLanes;if(a===0)var i=0;else{var f=n.suspendedLanes,h=n.pingedLanes;i=(1<<31-ae(42|t)+1)-1,i&=a&~(f&~h),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,Oh(n,i))}else i=st,i=Ja(n,n===Et?i:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(i&3)===0||Ln(n,i)||(l=!0,Oh(n,i));n=n.next}while(l);fc=!1}}function Gy(){xh()}function xh(){Vu=sc=!1;var t=0;kl!==0&&(Py()&&(t=kl),kl=0);for(var e=Oe(),l=null,n=Xu;n!==null;){var a=n.next,i=Ah(n,e);i===0?(n.next=null,l===null?Xu=a:l.next=a,a===null&&(Mn=l)):(l=n,(t!==0||(i&3)!==0)&&(Vu=!0)),n=a}_a(t)}function Ah(t,e){for(var l=t.suspendedLanes,n=t.pingedLanes,a=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var f=31-ae(i),h=1<<f,y=a[f];y===-1?((h&l)===0||(h&n)!==0)&&(a[f]=ym(h,e)):y<=e&&(t.expiredLanes|=h),i&=~h}if(e=Et,l=st,l=Ja(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n=t.callbackNode,l===0||t===e&&(yt===2||yt===9)||t.cancelPendingCommit!==null)return n!==null&&n!==null&&_i(n),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Ln(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(n!==null&&_i(n),Ai(l)){case 2:case 8:l=ms;break;case 32:l=Za;break;case 268435456:l=ys;break;default:l=Za}return n=Rh.bind(null,t),l=Ei(l,n),t.callbackPriority=e,t.callbackNode=l,e}return n!==null&&n!==null&&_i(n),t.callbackPriority=2,t.callbackNode=null,2}function Rh(t,e){if(Vt!==0&&Vt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Yu()&&t.callbackNode!==l)return null;var n=st;return n=Ja(t,t===Et?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n===0?null:(ch(t,n,e),Ah(t,Oe()),t.callbackNode!=null&&t.callbackNode===l?Rh.bind(null,t):null)}function Oh(t,e){if(Yu())return null;ch(t,e,!0)}function Qy(){tp(function(){(mt&6)!==0?Ei(ds,Gy):xh()})}function oc(){return kl===0&&(kl=ps()),kl}function Nh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Ia(""+t)}function Mh(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Zy(t,e,l,n,a){if(e==="submit"&&l&&l.stateNode===a){var i=Nh((a[Ft]||null).action),f=n.submitter;f&&(e=(e=f[Ft]||null)?Nh(e.formAction):f.getAttribute("formAction"),e!==null&&(i=e,f=null));var h=new nu("action","action",null,n,a);t.push({event:h,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(kl!==0){var y=f?Mh(a,f):new FormData(a);wr(l,{pending:!0,data:y,method:a.method,action:i},null,y)}}else typeof i=="function"&&(h.preventDefault(),y=f?Mh(a,f):new FormData(a),wr(l,{pending:!0,data:y,method:a.method,action:i},i,y))},currentTarget:a}]})}}for(var hc=0;hc<Wi.length;hc++){var dc=Wi[hc],ky=dc.toLowerCase(),Ky=dc[0].toUpperCase()+dc.slice(1);Te(ky,"on"+Ky)}Te(rf,"onAnimationEnd"),Te(cf,"onAnimationIteration"),Te(sf,"onAnimationStart"),Te("dblclick","onDoubleClick"),Te("focusin","onFocus"),Te("focusout","onBlur"),Te(fy,"onTransitionRun"),Te(oy,"onTransitionStart"),Te(hy,"onTransitionCancel"),Te(ff,"onTransitionEnd"),Pl("onMouseEnter",["mouseout","mouseover"]),Pl("onMouseLeave",["mouseout","mouseover"]),Pl("onPointerEnter",["pointerout","pointerover"]),Pl("onPointerLeave",["pointerout","pointerover"]),Dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ta="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Jy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ta));function Dh(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var n=t[l],a=n.event;n=n.listeners;t:{var i=void 0;if(e)for(var f=n.length-1;0<=f;f--){var h=n[f],y=h.instance,T=h.currentTarget;if(h=h.listener,y!==i&&a.isPropagationStopped())break t;i=h,a.currentTarget=T;try{i(a)}catch(N){Mu(N)}a.currentTarget=null,i=y}else for(f=0;f<n.length;f++){if(h=n[f],y=h.instance,T=h.currentTarget,h=h.listener,y!==i&&a.isPropagationStopped())break t;i=h,a.currentTarget=T;try{i(a)}catch(N){Mu(N)}a.currentTarget=null,i=y}}}}function rt(t,e){var l=e[Ri];l===void 0&&(l=e[Ri]=new Set);var n=t+"__bubble";l.has(n)||(wh(e,t,2,!1),l.add(n))}function mc(t,e,l){var n=0;e&&(n|=4),wh(l,t,n,e)}var Gu="_reactListening"+Math.random().toString(36).slice(2);function yc(t){if(!t[Gu]){t[Gu]=!0,_s.forEach(function(l){l!=="selectionchange"&&(Jy.has(l)||mc(l,!1,t),mc(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Gu]||(e[Gu]=!0,mc("selectionchange",!1,e))}}function wh(t,e,l,n){switch(ed(e)){case 2:var a=Ep;break;case 8:a=_p;break;default:a=Mc}l=a.bind(null,e,l,t),a=void 0,!qi||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(a=!0),n?a!==void 0?t.addEventListener(e,l,{capture:!0,passive:a}):t.addEventListener(e,l,!0):a!==void 0?t.addEventListener(e,l,{passive:a}):t.addEventListener(e,l,!1)}function pc(t,e,l,n,a){var i=n;if((e&1)===0&&(e&2)===0&&n!==null)t:for(;;){if(n===null)return;var f=n.tag;if(f===3||f===4){var h=n.stateNode.containerInfo;if(h===a)break;if(f===4)for(f=n.return;f!==null;){var y=f.tag;if((y===3||y===4)&&f.stateNode.containerInfo===a)return;f=f.return}for(;h!==null;){if(f=$l(h),f===null)return;if(y=f.tag,y===5||y===6||y===26||y===27){n=i=f;continue t}h=h.parentNode}}n=n.return}Hs(function(){var T=i,N=Bi(l),z=[];t:{var A=of.get(t);if(A!==void 0){var R=nu,tt=t;switch(t){case"keypress":if(eu(l)===0)break t;case"keydown":case"keyup":R=Vm;break;case"focusin":tt="focus",R=Xi;break;case"focusout":tt="blur",R=Xi;break;case"beforeblur":case"afterblur":R=Xi;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":R=js;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":R=Dm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":R=Zm;break;case rf:case cf:case sf:R=Cm;break;case ff:R=Km;break;case"scroll":case"scrollend":R=Nm;break;case"wheel":R=$m;break;case"copy":case"cut":case"paste":R=Bm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":R=Xs;break;case"toggle":case"beforetoggle":R=Fm}var W=(e&4)!==0,gt=!W&&(t==="scroll"||t==="scrollend"),S=W?A!==null?A+"Capture":null:A;W=[];for(var g=T,_;g!==null;){var w=g;if(_=w.stateNode,w=w.tag,w!==5&&w!==26&&w!==27||_===null||S===null||(w=Vn(g,S),w!=null&&W.push(xa(g,w,_))),gt)break;g=g.return}0<W.length&&(A=new R(A,tt,null,l,N),z.push({event:A,listeners:W}))}}if((e&7)===0){t:{if(A=t==="mouseover"||t==="pointerover",R=t==="mouseout"||t==="pointerout",A&&l!==Ui&&(tt=l.relatedTarget||l.fromElement)&&($l(tt)||tt[Jl]))break t;if((R||A)&&(A=N.window===N?N:(A=N.ownerDocument)?A.defaultView||A.parentWindow:window,R?(tt=l.relatedTarget||l.toElement,R=T,tt=tt?$l(tt):null,tt!==null&&(gt=d(tt),W=tt.tag,tt!==gt||W!==5&&W!==27&&W!==6)&&(tt=null)):(R=null,tt=T),R!==tt)){if(W=js,w="onMouseLeave",S="onMouseEnter",g="mouse",(t==="pointerout"||t==="pointerover")&&(W=Xs,w="onPointerLeave",S="onPointerEnter",g="pointer"),gt=R==null?A:Xn(R),_=tt==null?A:Xn(tt),A=new W(w,g+"leave",R,l,N),A.target=gt,A.relatedTarget=_,w=null,$l(N)===T&&(W=new W(S,g+"enter",tt,l,N),W.target=_,W.relatedTarget=gt,w=W),gt=w,R&&tt)e:{for(W=R,S=tt,g=0,_=W;_;_=Dn(_))g++;for(_=0,w=S;w;w=Dn(w))_++;for(;0<g-_;)W=Dn(W),g--;for(;0<_-g;)S=Dn(S),_--;for(;g--;){if(W===S||S!==null&&W===S.alternate)break e;W=Dn(W),S=Dn(S)}W=null}else W=null;R!==null&&zh(z,A,R,W,!1),tt!==null&&gt!==null&&zh(z,gt,tt,W,!0)}}t:{if(A=T?Xn(T):window,R=A.nodeName&&A.nodeName.toLowerCase(),R==="select"||R==="input"&&A.type==="file")var Z=$s;else if(Ks(A))if(Ws)Z=ry;else{Z=uy;var ut=ay}else R=A.nodeName,!R||R.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?T&&Ci(T.elementType)&&(Z=$s):Z=iy;if(Z&&(Z=Z(t,T))){Js(z,Z,l,N);break t}ut&&ut(t,A,T),t==="focusout"&&T&&A.type==="number"&&T.memoizedProps.value!=null&&zi(A,"number",A.value)}switch(ut=T?Xn(T):window,t){case"focusin":(Ks(ut)||ut.contentEditable==="true")&&(un=ut,Ki=T,Wn=null);break;case"focusout":Wn=Ki=un=null;break;case"mousedown":Ji=!0;break;case"contextmenu":case"mouseup":case"dragend":Ji=!1,af(z,l,N);break;case"selectionchange":if(sy)break;case"keydown":case"keyup":af(z,l,N)}var K;if(Gi)t:{switch(t){case"compositionstart":var F="onCompositionStart";break t;case"compositionend":F="onCompositionEnd";break t;case"compositionupdate":F="onCompositionUpdate";break t}F=void 0}else an?Zs(t,l)&&(F="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(F="onCompositionStart");F&&(Vs&&l.locale!=="ko"&&(an||F!=="onCompositionStart"?F==="onCompositionEnd"&&an&&(K=qs()):(al=N,Li="value"in al?al.value:al.textContent,an=!0)),ut=Qu(T,F),0<ut.length&&(F=new Ys(F,t,null,l,N),z.push({event:F,listeners:ut}),K?F.data=K:(K=ks(l),K!==null&&(F.data=K)))),(K=Im?ty(t,l):ey(t,l))&&(F=Qu(T,"onBeforeInput"),0<F.length&&(ut=new Ys("onBeforeInput","beforeinput",null,l,N),z.push({event:ut,listeners:F}),ut.data=K)),Zy(z,t,T,l,N)}Dh(z,e)})}function xa(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Qu(t,e){for(var l=e+"Capture",n=[];t!==null;){var a=t,i=a.stateNode;if(a=a.tag,a!==5&&a!==26&&a!==27||i===null||(a=Vn(t,l),a!=null&&n.unshift(xa(t,a,i)),a=Vn(t,e),a!=null&&n.push(xa(t,a,i))),t.tag===3)return n;t=t.return}return[]}function Dn(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function zh(t,e,l,n,a){for(var i=e._reactName,f=[];l!==null&&l!==n;){var h=l,y=h.alternate,T=h.stateNode;if(h=h.tag,y!==null&&y===n)break;h!==5&&h!==26&&h!==27||T===null||(y=T,a?(T=Vn(l,i),T!=null&&f.unshift(xa(l,T,y))):a||(T=Vn(l,i),T!=null&&f.push(xa(l,T,y)))),l=l.return}f.length!==0&&t.push({event:e,listeners:f})}var $y=/\r\n?/g,Wy=/\u0000|\uFFFD/g;function Ch(t){return(typeof t=="string"?t:""+t).replace($y,`
`).replace(Wy,"")}function Uh(t,e){return e=Ch(e),Ch(t)===e}function Zu(){}function vt(t,e,l,n,a,i){switch(l){case"children":typeof n=="string"?e==="body"||e==="textarea"&&n===""||en(t,n):(typeof n=="number"||typeof n=="bigint")&&e!=="body"&&en(t,""+n);break;case"className":Wa(t,"class",n);break;case"tabIndex":Wa(t,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":Wa(t,l,n);break;case"style":Us(t,n,i);break;case"data":if(e!=="object"){Wa(t,"data",n);break}case"src":case"href":if(n===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Ia(""+n),t.setAttribute(l,n);break;case"action":case"formAction":if(typeof n=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(e!=="input"&&vt(t,e,"name",a.name,a,null),vt(t,e,"formEncType",a.formEncType,a,null),vt(t,e,"formMethod",a.formMethod,a,null),vt(t,e,"formTarget",a.formTarget,a,null)):(vt(t,e,"encType",a.encType,a,null),vt(t,e,"method",a.method,a,null),vt(t,e,"target",a.target,a,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Ia(""+n),t.setAttribute(l,n);break;case"onClick":n!=null&&(t.onclick=Zu);break;case"onScroll":n!=null&&rt("scroll",t);break;case"onScrollEnd":n!=null&&rt("scrollend",t);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"multiple":t.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":t.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){t.removeAttribute("xlink:href");break}l=Ia(""+n),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""+n):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":n===!0?t.setAttribute(l,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,n):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?t.setAttribute(l,n):t.removeAttribute(l);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?t.removeAttribute(l):t.setAttribute(l,n);break;case"popover":rt("beforetoggle",t),rt("toggle",t),$a(t,"popover",n);break;case"xlinkActuate":Le(t,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":Le(t,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":Le(t,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":Le(t,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":Le(t,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":Le(t,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":Le(t,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":Le(t,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":Le(t,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":$a(t,"is",n);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Rm.get(l)||l,$a(t,l,n))}}function vc(t,e,l,n,a,i){switch(l){case"style":Us(t,n,i);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"children":typeof n=="string"?en(t,n):(typeof n=="number"||typeof n=="bigint")&&en(t,""+n);break;case"onScroll":n!=null&&rt("scroll",t);break;case"onScrollEnd":n!=null&&rt("scrollend",t);break;case"onClick":n!=null&&(t.onclick=Zu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ts.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(a=l.endsWith("Capture"),e=l.slice(2,a?l.length-7:void 0),i=t[Ft]||null,i=i!=null?i[l]:null,typeof i=="function"&&t.removeEventListener(e,i,a),typeof n=="function")){typeof i!="function"&&i!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,n,a);break t}l in t?t[l]=n:n===!0?t.setAttribute(l,""):$a(t,l,n)}}}function Gt(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":rt("error",t),rt("load",t);var n=!1,a=!1,i;for(i in l)if(l.hasOwnProperty(i)){var f=l[i];if(f!=null)switch(i){case"src":n=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:vt(t,e,i,f,l,null)}}a&&vt(t,e,"srcSet",l.srcSet,l,null),n&&vt(t,e,"src",l.src,l,null);return;case"input":rt("invalid",t);var h=i=f=a=null,y=null,T=null;for(n in l)if(l.hasOwnProperty(n)){var N=l[n];if(N!=null)switch(n){case"name":a=N;break;case"type":f=N;break;case"checked":y=N;break;case"defaultChecked":T=N;break;case"value":i=N;break;case"defaultValue":h=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(s(137,e));break;default:vt(t,e,n,N,l,null)}}Ds(t,i,h,y,T,f,a,!1),Fa(t);return;case"select":rt("invalid",t),n=f=i=null;for(a in l)if(l.hasOwnProperty(a)&&(h=l[a],h!=null))switch(a){case"value":i=h;break;case"defaultValue":f=h;break;case"multiple":n=h;default:vt(t,e,a,h,l,null)}e=i,l=f,t.multiple=!!n,e!=null?tn(t,!!n,e,!1):l!=null&&tn(t,!!n,l,!0);return;case"textarea":rt("invalid",t),i=a=n=null;for(f in l)if(l.hasOwnProperty(f)&&(h=l[f],h!=null))switch(f){case"value":n=h;break;case"defaultValue":a=h;break;case"children":i=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(s(91));break;default:vt(t,e,f,h,l,null)}zs(t,n,a,i),Fa(t);return;case"option":for(y in l)if(l.hasOwnProperty(y)&&(n=l[y],n!=null))switch(y){case"selected":t.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:vt(t,e,y,n,l,null)}return;case"dialog":rt("beforetoggle",t),rt("toggle",t),rt("cancel",t),rt("close",t);break;case"iframe":case"object":rt("load",t);break;case"video":case"audio":for(n=0;n<Ta.length;n++)rt(Ta[n],t);break;case"image":rt("error",t),rt("load",t);break;case"details":rt("toggle",t);break;case"embed":case"source":case"link":rt("error",t),rt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(T in l)if(l.hasOwnProperty(T)&&(n=l[T],n!=null))switch(T){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:vt(t,e,T,n,l,null)}return;default:if(Ci(e)){for(N in l)l.hasOwnProperty(N)&&(n=l[N],n!==void 0&&vc(t,e,N,n,l,void 0));return}}for(h in l)l.hasOwnProperty(h)&&(n=l[h],n!=null&&vt(t,e,h,n,l,null))}function Fy(t,e,l,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,i=null,f=null,h=null,y=null,T=null,N=null;for(R in l){var z=l[R];if(l.hasOwnProperty(R)&&z!=null)switch(R){case"checked":break;case"value":break;case"defaultValue":y=z;default:n.hasOwnProperty(R)||vt(t,e,R,null,n,z)}}for(var A in n){var R=n[A];if(z=l[A],n.hasOwnProperty(A)&&(R!=null||z!=null))switch(A){case"type":i=R;break;case"name":a=R;break;case"checked":T=R;break;case"defaultChecked":N=R;break;case"value":f=R;break;case"defaultValue":h=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(s(137,e));break;default:R!==z&&vt(t,e,A,R,n,z)}}wi(t,f,h,y,T,N,i,a);return;case"select":R=f=h=A=null;for(i in l)if(y=l[i],l.hasOwnProperty(i)&&y!=null)switch(i){case"value":break;case"multiple":R=y;default:n.hasOwnProperty(i)||vt(t,e,i,null,n,y)}for(a in n)if(i=n[a],y=l[a],n.hasOwnProperty(a)&&(i!=null||y!=null))switch(a){case"value":A=i;break;case"defaultValue":h=i;break;case"multiple":f=i;default:i!==y&&vt(t,e,a,i,n,y)}e=h,l=f,n=R,A!=null?tn(t,!!l,A,!1):!!n!=!!l&&(e!=null?tn(t,!!l,e,!0):tn(t,!!l,l?[]:"",!1));return;case"textarea":R=A=null;for(h in l)if(a=l[h],l.hasOwnProperty(h)&&a!=null&&!n.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:vt(t,e,h,null,n,a)}for(f in n)if(a=n[f],i=l[f],n.hasOwnProperty(f)&&(a!=null||i!=null))switch(f){case"value":A=a;break;case"defaultValue":R=a;break;case"children":break;case"dangerouslySetInnerHTML":if(a!=null)throw Error(s(91));break;default:a!==i&&vt(t,e,f,a,n,i)}ws(t,A,R);return;case"option":for(var tt in l)if(A=l[tt],l.hasOwnProperty(tt)&&A!=null&&!n.hasOwnProperty(tt))switch(tt){case"selected":t.selected=!1;break;default:vt(t,e,tt,null,n,A)}for(y in n)if(A=n[y],R=l[y],n.hasOwnProperty(y)&&A!==R&&(A!=null||R!=null))switch(y){case"selected":t.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:vt(t,e,y,A,n,R)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in l)A=l[W],l.hasOwnProperty(W)&&A!=null&&!n.hasOwnProperty(W)&&vt(t,e,W,null,n,A);for(T in n)if(A=n[T],R=l[T],n.hasOwnProperty(T)&&A!==R&&(A!=null||R!=null))switch(T){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(s(137,e));break;default:vt(t,e,T,A,n,R)}return;default:if(Ci(e)){for(var gt in l)A=l[gt],l.hasOwnProperty(gt)&&A!==void 0&&!n.hasOwnProperty(gt)&&vc(t,e,gt,void 0,n,A);for(N in n)A=n[N],R=l[N],!n.hasOwnProperty(N)||A===R||A===void 0&&R===void 0||vc(t,e,N,A,n,R);return}}for(var S in l)A=l[S],l.hasOwnProperty(S)&&A!=null&&!n.hasOwnProperty(S)&&vt(t,e,S,null,n,A);for(z in n)A=n[z],R=l[z],!n.hasOwnProperty(z)||A===R||A==null&&R==null||vt(t,e,z,A,n,R)}var gc=null,bc=null;function ku(t){return t.nodeType===9?t:t.ownerDocument}function Bh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Hh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Sc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Ec=null;function Py(){var t=window.event;return t&&t.type==="popstate"?t===Ec?!1:(Ec=t,!0):(Ec=null,!1)}var qh=typeof setTimeout=="function"?setTimeout:void 0,Iy=typeof clearTimeout=="function"?clearTimeout:void 0,Lh=typeof Promise=="function"?Promise:void 0,tp=typeof queueMicrotask=="function"?queueMicrotask:typeof Lh<"u"?function(t){return Lh.resolve(null).then(t).catch(ep)}:qh;function ep(t){setTimeout(function(){throw t})}function Sl(t){return t==="head"}function jh(t,e){var l=e,n=0,a=0;do{var i=l.nextSibling;if(t.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<n&&8>n){l=n;var f=t.ownerDocument;if(l&1&&Aa(f.documentElement),l&2&&Aa(f.body),l&4)for(l=f.head,Aa(l),f=l.firstChild;f;){var h=f.nextSibling,y=f.nodeName;f[Yn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&f.rel.toLowerCase()==="stylesheet"||l.removeChild(f),f=h}}if(a===0){t.removeChild(i),Ca(e);return}a--}else l==="$"||l==="$?"||l==="$!"?a++:n=l.charCodeAt(0)-48;else n=0;l=i}while(l);Ca(e)}function _c(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":_c(l),Oi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function lp(t,e,l,n){for(;t.nodeType===1;){var a=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!n&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(n){if(!t[Yn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==a.rel||t.getAttribute("href")!==(a.href==null||a.href===""?null:a.href)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin)||t.getAttribute("title")!==(a.title==null?null:a.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(a.src==null?null:a.src)||t.getAttribute("type")!==(a.type==null?null:a.type)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=a.name==null?null:""+a.name;if(a.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Ae(t.nextSibling),t===null)break}return null}function np(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Ae(t.nextSibling),t===null))return null;return t}function Tc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function ap(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var n=function(){e(),l.removeEventListener("DOMContentLoaded",n)};l.addEventListener("DOMContentLoaded",n),t._reactRetry=n}}function Ae(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var xc=null;function Yh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function Xh(t,e,l){switch(e=ku(l),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function Aa(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Oi(t)}var Ee=new Map,Vh=new Set;function Ku(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Pe=X.d;X.d={f:up,r:ip,D:rp,C:cp,L:sp,m:fp,X:hp,S:op,M:dp};function up(){var t=Pe.f(),e=Lu();return t||e}function ip(t){var e=Wl(t);e!==null&&e.tag===5&&e.type==="form"?ro(e):Pe.r(t)}var wn=typeof document>"u"?null:document;function Gh(t,e,l){var n=wn;if(n&&typeof e=="string"&&e){var a=me(e);a='link[rel="'+t+'"][href="'+a+'"]',typeof l=="string"&&(a+='[crossorigin="'+l+'"]'),Vh.has(a)||(Vh.add(a),t={rel:t,crossOrigin:l,href:e},n.querySelector(a)===null&&(e=n.createElement("link"),Gt(e,"link",t),qt(e),n.head.appendChild(e)))}}function rp(t){Pe.D(t),Gh("dns-prefetch",t,null)}function cp(t,e){Pe.C(t,e),Gh("preconnect",t,e)}function sp(t,e,l){Pe.L(t,e,l);var n=wn;if(n&&t&&e){var a='link[rel="preload"][as="'+me(e)+'"]';e==="image"&&l&&l.imageSrcSet?(a+='[imagesrcset="'+me(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(a+='[imagesizes="'+me(l.imageSizes)+'"]')):a+='[href="'+me(t)+'"]';var i=a;switch(e){case"style":i=zn(t);break;case"script":i=Cn(t)}Ee.has(i)||(t=E({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Ee.set(i,t),n.querySelector(a)!==null||e==="style"&&n.querySelector(Ra(i))||e==="script"&&n.querySelector(Oa(i))||(e=n.createElement("link"),Gt(e,"link",t),qt(e),n.head.appendChild(e)))}}function fp(t,e){Pe.m(t,e);var l=wn;if(l&&t){var n=e&&typeof e.as=="string"?e.as:"script",a='link[rel="modulepreload"][as="'+me(n)+'"][href="'+me(t)+'"]',i=a;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Cn(t)}if(!Ee.has(i)&&(t=E({rel:"modulepreload",href:t},e),Ee.set(i,t),l.querySelector(a)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Oa(i)))return}n=l.createElement("link"),Gt(n,"link",t),qt(n),l.head.appendChild(n)}}}function op(t,e,l){Pe.S(t,e,l);var n=wn;if(n&&t){var a=Fl(n).hoistableStyles,i=zn(t);e=e||"default";var f=a.get(i);if(!f){var h={loading:0,preload:null};if(f=n.querySelector(Ra(i)))h.loading=5;else{t=E({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Ee.get(i))&&Ac(t,l);var y=f=n.createElement("link");qt(y),Gt(y,"link",t),y._p=new Promise(function(T,N){y.onload=T,y.onerror=N}),y.addEventListener("load",function(){h.loading|=1}),y.addEventListener("error",function(){h.loading|=2}),h.loading|=4,Ju(f,e,n)}f={type:"stylesheet",instance:f,count:1,state:h},a.set(i,f)}}}function hp(t,e){Pe.X(t,e);var l=wn;if(l&&t){var n=Fl(l).hoistableScripts,a=Cn(t),i=n.get(a);i||(i=l.querySelector(Oa(a)),i||(t=E({src:t,async:!0},e),(e=Ee.get(a))&&Rc(t,e),i=l.createElement("script"),qt(i),Gt(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},n.set(a,i))}}function dp(t,e){Pe.M(t,e);var l=wn;if(l&&t){var n=Fl(l).hoistableScripts,a=Cn(t),i=n.get(a);i||(i=l.querySelector(Oa(a)),i||(t=E({src:t,async:!0,type:"module"},e),(e=Ee.get(a))&&Rc(t,e),i=l.createElement("script"),qt(i),Gt(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},n.set(a,i))}}function Qh(t,e,l,n){var a=(a=lt.current)?Ku(a):null;if(!a)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=zn(l.href),l=Fl(a).hoistableStyles,n=l.get(e),n||(n={type:"style",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=zn(l.href);var i=Fl(a).hoistableStyles,f=i.get(t);if(f||(a=a.ownerDocument||a,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,f),(i=a.querySelector(Ra(t)))&&!i._p&&(f.instance=i,f.state.loading=5),Ee.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ee.set(t,l),i||mp(a,t,l,f.state))),e&&n===null)throw Error(s(528,""));return f}if(e&&n!==null)throw Error(s(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Cn(l),l=Fl(a).hoistableScripts,n=l.get(e),n||(n={type:"script",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function zn(t){return'href="'+me(t)+'"'}function Ra(t){return'link[rel="stylesheet"]['+t+"]"}function Zh(t){return E({},t,{"data-precedence":t.precedence,precedence:null})}function mp(t,e,l,n){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?n.loading=1:(e=t.createElement("link"),n.preload=e,e.addEventListener("load",function(){return n.loading|=1}),e.addEventListener("error",function(){return n.loading|=2}),Gt(e,"link",l),qt(e),t.head.appendChild(e))}function Cn(t){return'[src="'+me(t)+'"]'}function Oa(t){return"script[async]"+t}function kh(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var n=t.querySelector('style[data-href~="'+me(l.href)+'"]');if(n)return e.instance=n,qt(n),n;var a=E({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return n=(t.ownerDocument||t).createElement("style"),qt(n),Gt(n,"style",a),Ju(n,l.precedence,t),e.instance=n;case"stylesheet":a=zn(l.href);var i=t.querySelector(Ra(a));if(i)return e.state.loading|=4,e.instance=i,qt(i),i;n=Zh(l),(a=Ee.get(a))&&Ac(n,a),i=(t.ownerDocument||t).createElement("link"),qt(i);var f=i;return f._p=new Promise(function(h,y){f.onload=h,f.onerror=y}),Gt(i,"link",n),e.state.loading|=4,Ju(i,l.precedence,t),e.instance=i;case"script":return i=Cn(l.src),(a=t.querySelector(Oa(i)))?(e.instance=a,qt(a),a):(n=l,(a=Ee.get(i))&&(n=E({},l),Rc(n,a)),t=t.ownerDocument||t,a=t.createElement("script"),qt(a),Gt(a,"link",n),t.head.appendChild(a),e.instance=a);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(n=e.instance,e.state.loading|=4,Ju(n,l.precedence,t));return e.instance}function Ju(t,e,l){for(var n=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=n.length?n[n.length-1]:null,i=a,f=0;f<n.length;f++){var h=n[f];if(h.dataset.precedence===e)i=h;else if(i!==a)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function Ac(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Rc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var $u=null;function Kh(t,e,l){if($u===null){var n=new Map,a=$u=new Map;a.set(l,n)}else a=$u,n=a.get(l),n||(n=new Map,a.set(l,n));if(n.has(t))return n;for(n.set(t,null),l=l.getElementsByTagName(t),a=0;a<l.length;a++){var i=l[a];if(!(i[Yn]||i[kt]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var f=i.getAttribute(e)||"";f=t+f;var h=n.get(f);h?h.push(i):n.set(f,[i])}}return n}function Jh(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function yp(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function $h(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Na=null;function pp(){}function vp(t,e,l){if(Na===null)throw Error(s(475));var n=Na;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var a=zn(l.href),i=t.querySelector(Ra(a));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(n.count++,n=Wu.bind(n),t.then(n,n)),e.state.loading|=4,e.instance=i,qt(i);return}i=t.ownerDocument||t,l=Zh(l),(a=Ee.get(a))&&Ac(l,a),i=i.createElement("link"),qt(i);var f=i;f._p=new Promise(function(h,y){f.onload=h,f.onerror=y}),Gt(i,"link",l),e.instance=i}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(n.count++,e=Wu.bind(n),t.addEventListener("load",e),t.addEventListener("error",e))}}function gp(){if(Na===null)throw Error(s(475));var t=Na;return t.stylesheets&&t.count===0&&Oc(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&Oc(t,t.stylesheets),t.unsuspend){var n=t.unsuspend;t.unsuspend=null,n()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Wu(){if(this.count--,this.count===0){if(this.stylesheets)Oc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Fu=null;function Oc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Fu=new Map,e.forEach(bp,t),Fu=null,Wu.call(t))}function bp(t,e){if(!(e.state.loading&4)){var l=Fu.get(t);if(l)var n=l.get(null);else{l=new Map,Fu.set(t,l);for(var a=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<a.length;i++){var f=a[i];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(l.set(f.dataset.precedence,f),n=f)}n&&l.set(null,n)}a=e.instance,f=a.getAttribute("data-precedence"),i=l.get(f)||n,i===n&&l.set(null,a),l.set(f,a),this.count++,n=Wu.bind(this),a.addEventListener("load",n),a.addEventListener("error",n),i?i.parentNode.insertBefore(a,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(a,t.firstChild)),e.state.loading|=4}}var Ma={$$typeof:Q,Provider:null,Consumer:null,_currentValue:I,_currentValue2:I,_threadCount:0};function Sp(t,e,l,n,a,i,f,h){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ti(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ti(0),this.hiddenUpdates=Ti(null),this.identifierPrefix=n,this.onUncaughtError=a,this.onCaughtError=i,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function Wh(t,e,l,n,a,i,f,h,y,T,N,z){return t=new Sp(t,e,l,f,h,y,T,z),e=1,i===!0&&(e|=24),i=ie(3,null,null,e),t.current=i,i.stateNode=t,e=cr(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:n,isDehydrated:l,cache:e},hr(i),t}function Fh(t){return t?(t=fn,t):fn}function Ph(t,e,l,n,a,i){a=Fh(a),n.context===null?n.context=a:n.pendingContext=a,n=rl(e),n.payload={element:l},i=i===void 0?null:i,i!==null&&(n.callback=i),l=cl(t,n,e),l!==null&&(oe(l,t,e),ua(l,t,e))}function Ih(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function Nc(t,e){Ih(t,e),(t=t.alternate)&&Ih(t,e)}function td(t){if(t.tag===13){var e=sn(t,67108864);e!==null&&oe(e,t,67108864),Nc(t,67108864)}}var Pu=!0;function Ep(t,e,l,n){var a=M.T;M.T=null;var i=X.p;try{X.p=2,Mc(t,e,l,n)}finally{X.p=i,M.T=a}}function _p(t,e,l,n){var a=M.T;M.T=null;var i=X.p;try{X.p=8,Mc(t,e,l,n)}finally{X.p=i,M.T=a}}function Mc(t,e,l,n){if(Pu){var a=Dc(n);if(a===null)pc(t,e,n,Iu,l),ld(t,n);else if(xp(a,t,e,l,n))n.stopPropagation();else if(ld(t,n),e&4&&-1<Tp.indexOf(t)){for(;a!==null;){var i=Wl(a);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var f=Ml(i.pendingLanes);if(f!==0){var h=i;for(h.pendingLanes|=2,h.entangledLanes|=2;f;){var y=1<<31-ae(f);h.entanglements[1]|=y,f&=~y}ze(i),(mt&6)===0&&(Hu=Oe()+500,_a(0))}}break;case 13:h=sn(i,2),h!==null&&oe(h,i,2),Lu(),Nc(i,2)}if(i=Dc(n),i===null&&pc(t,e,n,Iu,l),i===a)break;a=i}a!==null&&n.stopPropagation()}else pc(t,e,n,null,l)}}function Dc(t){return t=Bi(t),wc(t)}var Iu=null;function wc(t){if(Iu=null,t=$l(t),t!==null){var e=d(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=b(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Iu=t,null}function ed(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(cm()){case ds:return 2;case ms:return 8;case Za:case sm:return 32;case ys:return 268435456;default:return 32}default:return 32}}var zc=!1,El=null,_l=null,Tl=null,Da=new Map,wa=new Map,xl=[],Tp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ld(t,e){switch(t){case"focusin":case"focusout":El=null;break;case"dragenter":case"dragleave":_l=null;break;case"mouseover":case"mouseout":Tl=null;break;case"pointerover":case"pointerout":Da.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":wa.delete(e.pointerId)}}function za(t,e,l,n,a,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:l,eventSystemFlags:n,nativeEvent:i,targetContainers:[a]},e!==null&&(e=Wl(e),e!==null&&td(e)),t):(t.eventSystemFlags|=n,e=t.targetContainers,a!==null&&e.indexOf(a)===-1&&e.push(a),t)}function xp(t,e,l,n,a){switch(e){case"focusin":return El=za(El,t,e,l,n,a),!0;case"dragenter":return _l=za(_l,t,e,l,n,a),!0;case"mouseover":return Tl=za(Tl,t,e,l,n,a),!0;case"pointerover":var i=a.pointerId;return Da.set(i,za(Da.get(i)||null,t,e,l,n,a)),!0;case"gotpointercapture":return i=a.pointerId,wa.set(i,za(wa.get(i)||null,t,e,l,n,a)),!0}return!1}function nd(t){var e=$l(t.target);if(e!==null){var l=d(e);if(l!==null){if(e=l.tag,e===13){if(e=b(l),e!==null){t.blockedOn=e,vm(t.priority,function(){if(l.tag===13){var n=fe();n=xi(n);var a=sn(l,n);a!==null&&oe(a,l,n),Nc(l,n)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function ti(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Dc(t.nativeEvent);if(l===null){l=t.nativeEvent;var n=new l.constructor(l.type,l);Ui=n,l.target.dispatchEvent(n),Ui=null}else return e=Wl(l),e!==null&&td(e),t.blockedOn=l,!1;e.shift()}return!0}function ad(t,e,l){ti(t)&&l.delete(e)}function Ap(){zc=!1,El!==null&&ti(El)&&(El=null),_l!==null&&ti(_l)&&(_l=null),Tl!==null&&ti(Tl)&&(Tl=null),Da.forEach(ad),wa.forEach(ad)}function ei(t,e){t.blockedOn===e&&(t.blockedOn=null,zc||(zc=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ap)))}var li=null;function ud(t){li!==t&&(li=t,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){li===t&&(li=null);for(var e=0;e<t.length;e+=3){var l=t[e],n=t[e+1],a=t[e+2];if(typeof n!="function"){if(wc(n||l)===null)continue;break}var i=Wl(l);i!==null&&(t.splice(e,3),e-=3,wr(i,{pending:!0,data:a,method:l.method,action:n},n,a))}}))}function Ca(t){function e(y){return ei(y,t)}El!==null&&ei(El,t),_l!==null&&ei(_l,t),Tl!==null&&ei(Tl,t),Da.forEach(e),wa.forEach(e);for(var l=0;l<xl.length;l++){var n=xl[l];n.blockedOn===t&&(n.blockedOn=null)}for(;0<xl.length&&(l=xl[0],l.blockedOn===null);)nd(l),l.blockedOn===null&&xl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(n=0;n<l.length;n+=3){var a=l[n],i=l[n+1],f=a[Ft]||null;if(typeof i=="function")f||ud(l);else if(f){var h=null;if(i&&i.hasAttribute("formAction")){if(a=i,f=i[Ft]||null)h=f.formAction;else if(wc(a)!==null)continue}else h=f.action;typeof h=="function"?l[n+1]=h:(l.splice(n,3),n-=3),ud(l)}}}function Cc(t){this._internalRoot=t}ni.prototype.render=Cc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var l=e.current,n=fe();Ph(l,n,t,e,null,null)},ni.prototype.unmount=Cc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Ph(t.current,2,null,t,null,null),Lu(),e[Jl]=null}};function ni(t){this._internalRoot=t}ni.prototype.unstable_scheduleHydration=function(t){if(t){var e=Ss();t={blockedOn:null,target:t,priority:e};for(var l=0;l<xl.length&&e!==0&&e<xl[l].priority;l++);xl.splice(l,0,t),l===0&&nd(t)}};var id=u.version;if(id!=="19.1.0")throw Error(s(527,id,"19.1.0"));X.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=p(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var Rp={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ai=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ai.isDisabled&&ai.supportsFiber)try{qn=ai.inject(Rp),ne=ai}catch{}}return Ba.createRoot=function(t,e){if(!o(t))throw Error(s(299));var l=!1,n="",a=_o,i=To,f=xo,h=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(a=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(h=e.unstable_transitionCallbacks)),e=Wh(t,1,!1,null,null,l,n,a,i,f,h,null),t[Jl]=e.current,yc(t),new Cc(e)},Ba.hydrateRoot=function(t,e,l){if(!o(t))throw Error(s(299));var n=!1,a="",i=_o,f=To,h=xo,y=null,T=null;return l!=null&&(l.unstable_strictMode===!0&&(n=!0),l.identifierPrefix!==void 0&&(a=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(f=l.onCaughtError),l.onRecoverableError!==void 0&&(h=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(y=l.unstable_transitionCallbacks),l.formState!==void 0&&(T=l.formState)),e=Wh(t,1,!0,e,l??null,n,a,i,f,h,y,T),e.context=Fh(null),l=e.current,n=fe(),n=xi(n),a=rl(n),a.callback=null,cl(l,a,n),l=n,e.current.lanes=l,jn(e,l),ze(e),t[Jl]=e.current,yc(t),new ni(e)},Ba.version="19.1.0",Ba}var vd;function qp(){if(vd)return Hc.exports;vd=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(u){console.error(u)}}return r(),Hc.exports=Hp(),Hc.exports}var Lp=qp(),Ha={},gd;function jp(){if(gd)return Ha;gd=1,Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.parse=b,Ha.serialize=m;const r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,u=/^[\u0021-\u003A\u003C-\u007E]*$/,c=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,d=(()=>{const D=function(){};return D.prototype=Object.create(null),D})();function b(D,Y){const U=new d,L=D.length;if(L<2)return U;const V=Y?.decode||E;let B=0;do{const k=D.indexOf("=",B);if(k===-1)break;const Q=D.indexOf(";",B),P=Q===-1?L:Q;if(k>P){B=D.lastIndexOf(";",k-1)+1;continue}const J=x(D,B,k),_t=p(D,k,J),Tt=D.slice(J,_t);if(U[Tt]===void 0){let et=x(D,k+1,P),dt=p(D,P,et);const $t=V(D.slice(et,dt));U[Tt]=$t}B=P+1}while(B<L);return U}function x(D,Y,U){do{const L=D.charCodeAt(Y);if(L!==32&&L!==9)return Y}while(++Y<U);return U}function p(D,Y,U){for(;Y>U;){const L=D.charCodeAt(--Y);if(L!==32&&L!==9)return Y+1}return U}function m(D,Y,U){const L=U?.encode||encodeURIComponent;if(!r.test(D))throw new TypeError(`argument name is invalid: ${D}`);const V=L(Y);if(!u.test(V))throw new TypeError(`argument val is invalid: ${Y}`);let B=D+"="+V;if(!U)return B;if(U.maxAge!==void 0){if(!Number.isInteger(U.maxAge))throw new TypeError(`option maxAge is invalid: ${U.maxAge}`);B+="; Max-Age="+U.maxAge}if(U.domain){if(!c.test(U.domain))throw new TypeError(`option domain is invalid: ${U.domain}`);B+="; Domain="+U.domain}if(U.path){if(!s.test(U.path))throw new TypeError(`option path is invalid: ${U.path}`);B+="; Path="+U.path}if(U.expires){if(!C(U.expires)||!Number.isFinite(U.expires.valueOf()))throw new TypeError(`option expires is invalid: ${U.expires}`);B+="; Expires="+U.expires.toUTCString()}if(U.httpOnly&&(B+="; HttpOnly"),U.secure&&(B+="; Secure"),U.partitioned&&(B+="; Partitioned"),U.priority)switch(typeof U.priority=="string"?U.priority.toLowerCase():void 0){case"low":B+="; Priority=Low";break;case"medium":B+="; Priority=Medium";break;case"high":B+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${U.priority}`)}if(U.sameSite)switch(typeof U.sameSite=="string"?U.sameSite.toLowerCase():U.sameSite){case!0:case"strict":B+="; SameSite=Strict";break;case"lax":B+="; SameSite=Lax";break;case"none":B+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${U.sameSite}`)}return B}function E(D){if(D.indexOf("%")===-1)return D;try{return decodeURIComponent(D)}catch{return D}}function C(D){return o.call(D)==="[object Date]"}return Ha}jp();var bd="popstate";function Yp(r={}){function u(s,o){let{pathname:d,search:b,hash:x}=s.location;return Zc("",{pathname:d,search:b,hash:x},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function c(s,o){return typeof o=="string"?o:Ya(o)}return Vp(u,c,null,r)}function Ot(r,u){if(r===!1||r===null||typeof r>"u")throw new Error(u)}function Ue(r,u){if(!r){typeof console<"u"&&console.warn(u);try{throw new Error(u)}catch{}}}function Xp(){return Math.random().toString(36).substring(2,10)}function Sd(r,u){return{usr:r.state,key:r.key,idx:u}}function Zc(r,u,c=null,s){return{pathname:typeof r=="string"?r:r.pathname,search:"",hash:"",...typeof u=="string"?Un(u):u,state:c,key:u&&u.key||s||Xp()}}function Ya({pathname:r="/",search:u="",hash:c=""}){return u&&u!=="?"&&(r+=u.charAt(0)==="?"?u:"?"+u),c&&c!=="#"&&(r+=c.charAt(0)==="#"?c:"#"+c),r}function Un(r){let u={};if(r){let c=r.indexOf("#");c>=0&&(u.hash=r.substring(c),r=r.substring(0,c));let s=r.indexOf("?");s>=0&&(u.search=r.substring(s),r=r.substring(0,s)),r&&(u.pathname=r)}return u}function Vp(r,u,c,s={}){let{window:o=document.defaultView,v5Compat:d=!1}=s,b=o.history,x="POP",p=null,m=E();m==null&&(m=0,b.replaceState({...b.state,idx:m},""));function E(){return(b.state||{idx:null}).idx}function C(){x="POP";let V=E(),B=V==null?null:V-m;m=V,p&&p({action:x,location:L.location,delta:B})}function D(V,B){x="PUSH";let k=Zc(L.location,V,B);m=E()+1;let Q=Sd(k,m),P=L.createHref(k);try{b.pushState(Q,"",P)}catch(J){if(J instanceof DOMException&&J.name==="DataCloneError")throw J;o.location.assign(P)}d&&p&&p({action:x,location:L.location,delta:1})}function Y(V,B){x="REPLACE";let k=Zc(L.location,V,B);m=E();let Q=Sd(k,m),P=L.createHref(k);b.replaceState(Q,"",P),d&&p&&p({action:x,location:L.location,delta:0})}function U(V){return Gp(V)}let L={get action(){return x},get location(){return r(o,b)},listen(V){if(p)throw new Error("A history only accepts one active listener");return o.addEventListener(bd,C),p=V,()=>{o.removeEventListener(bd,C),p=null}},createHref(V){return u(o,V)},createURL:U,encodeLocation(V){let B=U(V);return{pathname:B.pathname,search:B.search,hash:B.hash}},push:D,replace:Y,go(V){return b.go(V)}};return L}function Gp(r,u=!1){let c="http://localhost";typeof window<"u"&&(c=window.location.origin!=="null"?window.location.origin:window.location.href),Ot(c,"No window.location.(origin|href) available to create URL");let s=typeof r=="string"?r:Ya(r);return s=s.replace(/ $/,"%20"),!u&&s.startsWith("//")&&(s=c+s),new URL(s,c)}function wd(r,u,c="/"){return Qp(r,u,c,!1)}function Qp(r,u,c,s){let o=typeof u=="string"?Un(u):u,d=tl(o.pathname||"/",c);if(d==null)return null;let b=zd(r);Zp(b);let x=null;for(let p=0;x==null&&p<b.length;++p){let m=l0(d);x=t0(b[p],m,s)}return x}function zd(r,u=[],c=[],s=""){let o=(d,b,x)=>{let p={relativePath:x===void 0?d.path||"":x,caseSensitive:d.caseSensitive===!0,childrenIndex:b,route:d};p.relativePath.startsWith("/")&&(Ot(p.relativePath.startsWith(s),`Absolute route path "${p.relativePath}" nested under path "${s}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(s.length));let m=Ie([s,p.relativePath]),E=c.concat(p);d.children&&d.children.length>0&&(Ot(d.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),zd(d.children,u,E,m)),!(d.path==null&&!d.index)&&u.push({path:m,score:Pp(m,d.index),routesMeta:E})};return r.forEach((d,b)=>{if(d.path===""||!d.path?.includes("?"))o(d,b);else for(let x of Cd(d.path))o(d,b,x)}),u}function Cd(r){let u=r.split("/");if(u.length===0)return[];let[c,...s]=u,o=c.endsWith("?"),d=c.replace(/\?$/,"");if(s.length===0)return o?[d,""]:[d];let b=Cd(s.join("/")),x=[];return x.push(...b.map(p=>p===""?d:[d,p].join("/"))),o&&x.push(...b),x.map(p=>r.startsWith("/")&&p===""?"/":p)}function Zp(r){r.sort((u,c)=>u.score!==c.score?c.score-u.score:Ip(u.routesMeta.map(s=>s.childrenIndex),c.routesMeta.map(s=>s.childrenIndex)))}var kp=/^:[\w-]+$/,Kp=3,Jp=2,$p=1,Wp=10,Fp=-2,Ed=r=>r==="*";function Pp(r,u){let c=r.split("/"),s=c.length;return c.some(Ed)&&(s+=Fp),u&&(s+=Jp),c.filter(o=>!Ed(o)).reduce((o,d)=>o+(kp.test(d)?Kp:d===""?$p:Wp),s)}function Ip(r,u){return r.length===u.length&&r.slice(0,-1).every((s,o)=>s===u[o])?r[r.length-1]-u[u.length-1]:0}function t0(r,u,c=!1){let{routesMeta:s}=r,o={},d="/",b=[];for(let x=0;x<s.length;++x){let p=s[x],m=x===s.length-1,E=d==="/"?u:u.slice(d.length)||"/",C=mi({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},E),D=p.route;if(!C&&m&&c&&!s[s.length-1].route.index&&(C=mi({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},E)),!C)return null;Object.assign(o,C.params),b.push({params:o,pathname:Ie([d,C.pathname]),pathnameBase:i0(Ie([d,C.pathnameBase])),route:D}),C.pathnameBase!=="/"&&(d=Ie([d,C.pathnameBase]))}return b}function mi(r,u){typeof r=="string"&&(r={path:r,caseSensitive:!1,end:!0});let[c,s]=e0(r.path,r.caseSensitive,r.end),o=u.match(c);if(!o)return null;let d=o[0],b=d.replace(/(.)\/+$/,"$1"),x=o.slice(1);return{params:s.reduce((m,{paramName:E,isOptional:C},D)=>{if(E==="*"){let U=x[D]||"";b=d.slice(0,d.length-U.length).replace(/(.)\/+$/,"$1")}const Y=x[D];return C&&!Y?m[E]=void 0:m[E]=(Y||"").replace(/%2F/g,"/"),m},{}),pathname:d,pathnameBase:b,pattern:r}}function e0(r,u=!1,c=!0){Ue(r==="*"||!r.endsWith("*")||r.endsWith("/*"),`Route path "${r}" will be treated as if it were "${r.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${r.replace(/\*$/,"/*")}".`);let s=[],o="^"+r.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(b,x,p)=>(s.push({paramName:x,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return r.endsWith("*")?(s.push({paramName:"*"}),o+=r==="*"||r==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):c?o+="\\/*$":r!==""&&r!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,u?void 0:"i"),s]}function l0(r){try{return r.split("/").map(u=>decodeURIComponent(u).replace(/\//g,"%2F")).join("/")}catch(u){return Ue(!1,`The URL path "${r}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${u}).`),r}}function tl(r,u){if(u==="/")return r;if(!r.toLowerCase().startsWith(u.toLowerCase()))return null;let c=u.endsWith("/")?u.length-1:u.length,s=r.charAt(c);return s&&s!=="/"?null:r.slice(c)||"/"}function n0(r,u="/"){let{pathname:c,search:s="",hash:o=""}=typeof r=="string"?Un(r):r;return{pathname:c?c.startsWith("/")?c:a0(c,u):u,search:r0(s),hash:c0(o)}}function a0(r,u){let c=u.replace(/\/+$/,"").split("/");return r.split("/").forEach(o=>{o===".."?c.length>1&&c.pop():o!=="."&&c.push(o)}),c.length>1?c.join("/"):"/"}function Yc(r,u,c,s){return`Cannot include a '${r}' character in a manually specified \`to.${u}\` field [${JSON.stringify(s)}].  Please separate it out to the \`to.${c}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function u0(r){return r.filter((u,c)=>c===0||u.route.path&&u.route.path.length>0)}function Ud(r){let u=u0(r);return u.map((c,s)=>s===u.length-1?c.pathname:c.pathnameBase)}function Bd(r,u,c,s=!1){let o;typeof r=="string"?o=Un(r):(o={...r},Ot(!o.pathname||!o.pathname.includes("?"),Yc("?","pathname","search",o)),Ot(!o.pathname||!o.pathname.includes("#"),Yc("#","pathname","hash",o)),Ot(!o.search||!o.search.includes("#"),Yc("#","search","hash",o)));let d=r===""||o.pathname==="",b=d?"/":o.pathname,x;if(b==null)x=c;else{let C=u.length-1;if(!s&&b.startsWith("..")){let D=b.split("/");for(;D[0]==="..";)D.shift(),C-=1;o.pathname=D.join("/")}x=C>=0?u[C]:"/"}let p=n0(o,x),m=b&&b!=="/"&&b.endsWith("/"),E=(d||b===".")&&c.endsWith("/");return!p.pathname.endsWith("/")&&(m||E)&&(p.pathname+="/"),p}var Ie=r=>r.join("/").replace(/\/\/+/g,"/"),i0=r=>r.replace(/\/+$/,"").replace(/^\/*/,"/"),r0=r=>!r||r==="?"?"":r.startsWith("?")?r:"?"+r,c0=r=>!r||r==="#"?"":r.startsWith("#")?r:"#"+r;function s0(r){return r!=null&&typeof r.status=="number"&&typeof r.statusText=="string"&&typeof r.internal=="boolean"&&"data"in r}var Hd=["POST","PUT","PATCH","DELETE"];new Set(Hd);var f0=["GET",...Hd];new Set(f0);var Bn=O.createContext(null);Bn.displayName="DataRouter";var yi=O.createContext(null);yi.displayName="DataRouterState";var qd=O.createContext({isTransitioning:!1});qd.displayName="ViewTransition";var o0=O.createContext(new Map);o0.displayName="Fetchers";var h0=O.createContext(null);h0.displayName="Await";var He=O.createContext(null);He.displayName="Navigation";var Xa=O.createContext(null);Xa.displayName="Location";var qe=O.createContext({outlet:null,matches:[],isDataRoute:!1});qe.displayName="Route";var es=O.createContext(null);es.displayName="RouteError";function d0(r,{relative:u}={}){Ot(Va(),"useHref() may be used only in the context of a <Router> component.");let{basename:c,navigator:s}=O.useContext(He),{hash:o,pathname:d,search:b}=Ga(r,{relative:u}),x=d;return c!=="/"&&(x=d==="/"?c:Ie([c,d])),s.createHref({pathname:x,search:b,hash:o})}function Va(){return O.useContext(Xa)!=null}function Kl(){return Ot(Va(),"useLocation() may be used only in the context of a <Router> component."),O.useContext(Xa).location}var Ld="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function jd(r){O.useContext(He).static||O.useLayoutEffect(r)}function m0(){let{isDataRoute:r}=O.useContext(qe);return r?N0():y0()}function y0(){Ot(Va(),"useNavigate() may be used only in the context of a <Router> component.");let r=O.useContext(Bn),{basename:u,navigator:c}=O.useContext(He),{matches:s}=O.useContext(qe),{pathname:o}=Kl(),d=JSON.stringify(Ud(s)),b=O.useRef(!1);return jd(()=>{b.current=!0}),O.useCallback((p,m={})=>{if(Ue(b.current,Ld),!b.current)return;if(typeof p=="number"){c.go(p);return}let E=Bd(p,JSON.parse(d),o,m.relative==="path");r==null&&u!=="/"&&(E.pathname=E.pathname==="/"?u:Ie([u,E.pathname])),(m.replace?c.replace:c.push)(E,m.state,m)},[u,c,d,o,r])}O.createContext(null);function p0(){let{matches:r}=O.useContext(qe),u=r[r.length-1];return u?u.params:{}}function Ga(r,{relative:u}={}){let{matches:c}=O.useContext(qe),{pathname:s}=Kl(),o=JSON.stringify(Ud(c));return O.useMemo(()=>Bd(r,JSON.parse(o),s,u==="path"),[r,o,s,u])}function v0(r,u){return Yd(r,u)}function Yd(r,u,c,s){Ot(Va(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=O.useContext(He),{matches:d}=O.useContext(qe),b=d[d.length-1],x=b?b.params:{},p=b?b.pathname:"/",m=b?b.pathnameBase:"/",E=b&&b.route;{let B=E&&E.path||"";Xd(p,!E||B.endsWith("*")||B.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${B}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${B}"> to <Route path="${B==="/"?"*":`${B}/*`}">.`)}let C=Kl(),D;if(u){let B=typeof u=="string"?Un(u):u;Ot(m==="/"||B.pathname?.startsWith(m),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${B.pathname}" was given in the \`location\` prop.`),D=B}else D=C;let Y=D.pathname||"/",U=Y;if(m!=="/"){let B=m.replace(/^\//,"").split("/");U="/"+Y.replace(/^\//,"").split("/").slice(B.length).join("/")}let L=wd(r,{pathname:U});Ue(E||L!=null,`No routes matched location "${D.pathname}${D.search}${D.hash}" `),Ue(L==null||L[L.length-1].route.element!==void 0||L[L.length-1].route.Component!==void 0||L[L.length-1].route.lazy!==void 0,`Matched leaf route at location "${D.pathname}${D.search}${D.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let V=_0(L&&L.map(B=>Object.assign({},B,{params:Object.assign({},x,B.params),pathname:Ie([m,o.encodeLocation?o.encodeLocation(B.pathname).pathname:B.pathname]),pathnameBase:B.pathnameBase==="/"?m:Ie([m,o.encodeLocation?o.encodeLocation(B.pathnameBase).pathname:B.pathnameBase])})),d,c,s);return u&&V?O.createElement(Xa.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...D},navigationType:"POP"}},V):V}function g0(){let r=O0(),u=s0(r)?`${r.status} ${r.statusText}`:r instanceof Error?r.message:JSON.stringify(r),c=r instanceof Error?r.stack:null,s="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:s},d={padding:"2px 4px",backgroundColor:s},b=null;return console.error("Error handled by React Router default ErrorBoundary:",r),b=O.createElement(O.Fragment,null,O.createElement("p",null,"💿 Hey developer 👋"),O.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",O.createElement("code",{style:d},"ErrorBoundary")," or"," ",O.createElement("code",{style:d},"errorElement")," prop on your route.")),O.createElement(O.Fragment,null,O.createElement("h2",null,"Unexpected Application Error!"),O.createElement("h3",{style:{fontStyle:"italic"}},u),c?O.createElement("pre",{style:o},c):null,b)}var b0=O.createElement(g0,null),S0=class extends O.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,u){return u.location!==r.location||u.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:u.error,location:u.location,revalidation:r.revalidation||u.revalidation}}componentDidCatch(r,u){console.error("React Router caught the following error during render",r,u)}render(){return this.state.error!==void 0?O.createElement(qe.Provider,{value:this.props.routeContext},O.createElement(es.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function E0({routeContext:r,match:u,children:c}){let s=O.useContext(Bn);return s&&s.static&&s.staticContext&&(u.route.errorElement||u.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=u.route.id),O.createElement(qe.Provider,{value:r},c)}function _0(r,u=[],c=null,s=null){if(r==null){if(!c)return null;if(c.errors)r=c.matches;else if(u.length===0&&!c.initialized&&c.matches.length>0)r=c.matches;else return null}let o=r,d=c?.errors;if(d!=null){let p=o.findIndex(m=>m.route.id&&d?.[m.route.id]!==void 0);Ot(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(d).join(",")}`),o=o.slice(0,Math.min(o.length,p+1))}let b=!1,x=-1;if(c)for(let p=0;p<o.length;p++){let m=o[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(x=p),m.route.id){let{loaderData:E,errors:C}=c,D=m.route.loader&&!E.hasOwnProperty(m.route.id)&&(!C||C[m.route.id]===void 0);if(m.route.lazy||D){b=!0,x>=0?o=o.slice(0,x+1):o=[o[0]];break}}}return o.reduceRight((p,m,E)=>{let C,D=!1,Y=null,U=null;c&&(C=d&&m.route.id?d[m.route.id]:void 0,Y=m.route.errorElement||b0,b&&(x<0&&E===0?(Xd("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),D=!0,U=null):x===E&&(D=!0,U=m.route.hydrateFallbackElement||null)));let L=u.concat(o.slice(0,E+1)),V=()=>{let B;return C?B=Y:D?B=U:m.route.Component?B=O.createElement(m.route.Component,null):m.route.element?B=m.route.element:B=p,O.createElement(E0,{match:m,routeContext:{outlet:p,matches:L,isDataRoute:c!=null},children:B})};return c&&(m.route.ErrorBoundary||m.route.errorElement||E===0)?O.createElement(S0,{location:c.location,revalidation:c.revalidation,component:Y,error:C,children:V(),routeContext:{outlet:null,matches:L,isDataRoute:!0}}):V()},null)}function ls(r){return`${r} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function T0(r){let u=O.useContext(Bn);return Ot(u,ls(r)),u}function x0(r){let u=O.useContext(yi);return Ot(u,ls(r)),u}function A0(r){let u=O.useContext(qe);return Ot(u,ls(r)),u}function ns(r){let u=A0(r),c=u.matches[u.matches.length-1];return Ot(c.route.id,`${r} can only be used on routes that contain a unique "id"`),c.route.id}function R0(){return ns("useRouteId")}function O0(){let r=O.useContext(es),u=x0("useRouteError"),c=ns("useRouteError");return r!==void 0?r:u.errors?.[c]}function N0(){let{router:r}=T0("useNavigate"),u=ns("useNavigate"),c=O.useRef(!1);return jd(()=>{c.current=!0}),O.useCallback(async(o,d={})=>{Ue(c.current,Ld),c.current&&(typeof o=="number"?r.navigate(o):await r.navigate(o,{fromRouteId:u,...d}))},[r,u])}var _d={};function Xd(r,u,c){!u&&!_d[r]&&(_d[r]=!0,Ue(!1,c))}O.memo(M0);function M0({routes:r,future:u,state:c}){return Yd(r,void 0,c,u)}function kc(r){Ot(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function D0({basename:r="/",children:u=null,location:c,navigationType:s="POP",navigator:o,static:d=!1}){Ot(!Va(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let b=r.replace(/^\/*/,"/"),x=O.useMemo(()=>({basename:b,navigator:o,static:d,future:{}}),[b,o,d]);typeof c=="string"&&(c=Un(c));let{pathname:p="/",search:m="",hash:E="",state:C=null,key:D="default"}=c,Y=O.useMemo(()=>{let U=tl(p,b);return U==null?null:{location:{pathname:U,search:m,hash:E,state:C,key:D},navigationType:s}},[b,p,m,E,C,D,s]);return Ue(Y!=null,`<Router basename="${b}"> is not able to match the URL "${p}${m}${E}" because it does not start with the basename, so the <Router> won't render anything.`),Y==null?null:O.createElement(He.Provider,{value:x},O.createElement(Xa.Provider,{children:u,value:Y}))}function w0({children:r,location:u}){return v0(Kc(r),u)}function Kc(r,u=[]){let c=[];return O.Children.forEach(r,(s,o)=>{if(!O.isValidElement(s))return;let d=[...u,o];if(s.type===O.Fragment){c.push.apply(c,Kc(s.props.children,d));return}Ot(s.type===kc,`[${typeof s.type=="string"?s.type:s.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ot(!s.props.index||!s.props.children,"An index route cannot have child routes.");let b={id:s.props.id||d.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,hydrateFallbackElement:s.props.hydrateFallbackElement,HydrateFallback:s.props.HydrateFallback,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.hasErrorBoundary===!0||s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(b.children=Kc(s.props.children,d)),c.push(b)}),c}var ci="get",si="application/x-www-form-urlencoded";function pi(r){return r!=null&&typeof r.tagName=="string"}function z0(r){return pi(r)&&r.tagName.toLowerCase()==="button"}function C0(r){return pi(r)&&r.tagName.toLowerCase()==="form"}function U0(r){return pi(r)&&r.tagName.toLowerCase()==="input"}function B0(r){return!!(r.metaKey||r.altKey||r.ctrlKey||r.shiftKey)}function H0(r,u){return r.button===0&&(!u||u==="_self")&&!B0(r)}var ui=null;function q0(){if(ui===null)try{new FormData(document.createElement("form"),0),ui=!1}catch{ui=!0}return ui}var L0=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Xc(r){return r!=null&&!L0.has(r)?(Ue(!1,`"${r}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${si}"`),null):r}function j0(r,u){let c,s,o,d,b;if(C0(r)){let x=r.getAttribute("action");s=x?tl(x,u):null,c=r.getAttribute("method")||ci,o=Xc(r.getAttribute("enctype"))||si,d=new FormData(r)}else if(z0(r)||U0(r)&&(r.type==="submit"||r.type==="image")){let x=r.form;if(x==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=r.getAttribute("formaction")||x.getAttribute("action");if(s=p?tl(p,u):null,c=r.getAttribute("formmethod")||x.getAttribute("method")||ci,o=Xc(r.getAttribute("formenctype"))||Xc(x.getAttribute("enctype"))||si,d=new FormData(x,r),!q0()){let{name:m,type:E,value:C}=r;if(E==="image"){let D=m?`${m}.`:"";d.append(`${D}x`,"0"),d.append(`${D}y`,"0")}else m&&d.append(m,C)}}else{if(pi(r))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');c=ci,s=null,o=si,b=r}return d&&o==="text/plain"&&(b=d,d=void 0),{action:s,method:c.toLowerCase(),encType:o,formData:d,body:b}}function as(r,u){if(r===!1||r===null||typeof r>"u")throw new Error(u)}async function Y0(r,u){if(r.id in u)return u[r.id];try{let c=await import(r.module);return u[r.id]=c,c}catch(c){return console.error(`Error loading route module \`${r.module}\`, reloading page...`),console.error(c),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function X0(r){return r==null?!1:r.href==null?r.rel==="preload"&&typeof r.imageSrcSet=="string"&&typeof r.imageSizes=="string":typeof r.rel=="string"&&typeof r.href=="string"}async function V0(r,u,c){let s=await Promise.all(r.map(async o=>{let d=u.routes[o.route.id];if(d){let b=await Y0(d,c);return b.links?b.links():[]}return[]}));return k0(s.flat(1).filter(X0).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function Td(r,u,c,s,o,d){let b=(p,m)=>c[m]?p.route.id!==c[m].route.id:!0,x=(p,m)=>c[m].pathname!==p.pathname||c[m].route.path?.endsWith("*")&&c[m].params["*"]!==p.params["*"];return d==="assets"?u.filter((p,m)=>b(p,m)||x(p,m)):d==="data"?u.filter((p,m)=>{let E=s.routes[p.route.id];if(!E||!E.hasLoader)return!1;if(b(p,m)||x(p,m))return!0;if(p.route.shouldRevalidate){let C=p.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:c[0]?.params||{},nextUrl:new URL(r,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof C=="boolean")return C}return!0}):[]}function G0(r,u,{includeHydrateFallback:c}={}){return Q0(r.map(s=>{let o=u.routes[s.route.id];if(!o)return[];let d=[o.module];return o.clientActionModule&&(d=d.concat(o.clientActionModule)),o.clientLoaderModule&&(d=d.concat(o.clientLoaderModule)),c&&o.hydrateFallbackModule&&(d=d.concat(o.hydrateFallbackModule)),o.imports&&(d=d.concat(o.imports)),d}).flat(1))}function Q0(r){return[...new Set(r)]}function Z0(r){let u={},c=Object.keys(r).sort();for(let s of c)u[s]=r[s];return u}function k0(r,u){let c=new Set;return new Set(u),r.reduce((s,o)=>{let d=JSON.stringify(Z0(o));return c.has(d)||(c.add(d),s.push({key:d,link:o})),s},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var K0=new Set([100,101,204,205]);function J0(r,u){let c=typeof r=="string"?new URL(r,typeof window>"u"?"server://singlefetch/":window.location.origin):r;return c.pathname==="/"?c.pathname="_root.data":u&&tl(c.pathname,u)==="/"?c.pathname=`${u.replace(/\/$/,"")}/_root.data`:c.pathname=`${c.pathname.replace(/\/$/,"")}.data`,c}function Vd(){let r=O.useContext(Bn);return as(r,"You must render this element inside a <DataRouterContext.Provider> element"),r}function $0(){let r=O.useContext(yi);return as(r,"You must render this element inside a <DataRouterStateContext.Provider> element"),r}var us=O.createContext(void 0);us.displayName="FrameworkContext";function Gd(){let r=O.useContext(us);return as(r,"You must render this element inside a <HydratedRouter> element"),r}function W0(r,u){let c=O.useContext(us),[s,o]=O.useState(!1),[d,b]=O.useState(!1),{onFocus:x,onBlur:p,onMouseEnter:m,onMouseLeave:E,onTouchStart:C}=u,D=O.useRef(null);O.useEffect(()=>{if(r==="render"&&b(!0),r==="viewport"){let L=B=>{B.forEach(k=>{b(k.isIntersecting)})},V=new IntersectionObserver(L,{threshold:.5});return D.current&&V.observe(D.current),()=>{V.disconnect()}}},[r]),O.useEffect(()=>{if(s){let L=setTimeout(()=>{b(!0)},100);return()=>{clearTimeout(L)}}},[s]);let Y=()=>{o(!0)},U=()=>{o(!1),b(!1)};return c?r!=="intent"?[d,D,{}]:[d,D,{onFocus:qa(x,Y),onBlur:qa(p,U),onMouseEnter:qa(m,Y),onMouseLeave:qa(E,U),onTouchStart:qa(C,Y)}]:[!1,D,{}]}function qa(r,u){return c=>{r&&r(c),c.defaultPrevented||u(c)}}function F0({page:r,...u}){let{router:c}=Vd(),s=O.useMemo(()=>wd(c.routes,r,c.basename),[c.routes,r,c.basename]);return s?O.createElement(I0,{page:r,matches:s,...u}):null}function P0(r){let{manifest:u,routeModules:c}=Gd(),[s,o]=O.useState([]);return O.useEffect(()=>{let d=!1;return V0(r,u,c).then(b=>{d||o(b)}),()=>{d=!0}},[r,u,c]),s}function I0({page:r,matches:u,...c}){let s=Kl(),{manifest:o,routeModules:d}=Gd(),{basename:b}=Vd(),{loaderData:x,matches:p}=$0(),m=O.useMemo(()=>Td(r,u,p,o,s,"data"),[r,u,p,o,s]),E=O.useMemo(()=>Td(r,u,p,o,s,"assets"),[r,u,p,o,s]),C=O.useMemo(()=>{if(r===s.pathname+s.search+s.hash)return[];let U=new Set,L=!1;if(u.forEach(B=>{let k=o.routes[B.route.id];!k||!k.hasLoader||(!m.some(Q=>Q.route.id===B.route.id)&&B.route.id in x&&d[B.route.id]?.shouldRevalidate||k.hasClientLoader?L=!0:U.add(B.route.id))}),U.size===0)return[];let V=J0(r,b);return L&&U.size>0&&V.searchParams.set("_routes",u.filter(B=>U.has(B.route.id)).map(B=>B.route.id).join(",")),[V.pathname+V.search]},[b,x,s,o,m,u,r,d]),D=O.useMemo(()=>G0(E,o),[E,o]),Y=P0(E);return O.createElement(O.Fragment,null,C.map(U=>O.createElement("link",{key:U,rel:"prefetch",as:"fetch",href:U,...c})),D.map(U=>O.createElement("link",{key:U,rel:"modulepreload",href:U,...c})),Y.map(({key:U,link:L})=>O.createElement("link",{key:U,...L})))}function tv(...r){return u=>{r.forEach(c=>{typeof c=="function"?c(u):c!=null&&(c.current=u)})}}var Qd=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Qd&&(window.__reactRouterVersion="7.6.2")}catch{}function ev({basename:r,children:u,window:c}){let s=O.useRef();s.current==null&&(s.current=Yp({window:c,v5Compat:!0}));let o=s.current,[d,b]=O.useState({action:o.action,location:o.location}),x=O.useCallback(p=>{O.startTransition(()=>b(p))},[b]);return O.useLayoutEffect(()=>o.listen(x),[o,x]),O.createElement(D0,{basename:r,children:u,location:d.location,navigationType:d.action,navigator:o})}var Zd=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,is=O.forwardRef(function({onClick:u,discover:c="render",prefetch:s="none",relative:o,reloadDocument:d,replace:b,state:x,target:p,to:m,preventScrollReset:E,viewTransition:C,...D},Y){let{basename:U}=O.useContext(He),L=typeof m=="string"&&Zd.test(m),V,B=!1;if(typeof m=="string"&&L&&(V=m,Qd))try{let dt=new URL(window.location.href),$t=m.startsWith("//")?new URL(dt.protocol+m):new URL(m),he=tl($t.pathname,U);$t.origin===dt.origin&&he!=null?m=he+$t.search+$t.hash:B=!0}catch{Ue(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let k=d0(m,{relative:o}),[Q,P,J]=W0(s,D),_t=uv(m,{replace:b,state:x,target:p,preventScrollReset:E,relative:o,viewTransition:C});function Tt(dt){u&&u(dt),dt.defaultPrevented||_t(dt)}let et=O.createElement("a",{...D,...J,href:V||k,onClick:B||d?u:Tt,ref:tv(Y,P),target:p,"data-discover":!L&&c==="render"?"true":void 0});return Q&&!L?O.createElement(O.Fragment,null,et,O.createElement(F0,{page:k})):et});is.displayName="Link";var lv=O.forwardRef(function({"aria-current":u="page",caseSensitive:c=!1,className:s="",end:o=!1,style:d,to:b,viewTransition:x,children:p,...m},E){let C=Ga(b,{relative:m.relative}),D=Kl(),Y=O.useContext(yi),{navigator:U,basename:L}=O.useContext(He),V=Y!=null&&fv(C)&&x===!0,B=U.encodeLocation?U.encodeLocation(C).pathname:C.pathname,k=D.pathname,Q=Y&&Y.navigation&&Y.navigation.location?Y.navigation.location.pathname:null;c||(k=k.toLowerCase(),Q=Q?Q.toLowerCase():null,B=B.toLowerCase()),Q&&L&&(Q=tl(Q,L)||Q);const P=B!=="/"&&B.endsWith("/")?B.length-1:B.length;let J=k===B||!o&&k.startsWith(B)&&k.charAt(P)==="/",_t=Q!=null&&(Q===B||!o&&Q.startsWith(B)&&Q.charAt(B.length)==="/"),Tt={isActive:J,isPending:_t,isTransitioning:V},et=J?u:void 0,dt;typeof s=="function"?dt=s(Tt):dt=[s,J?"active":null,_t?"pending":null,V?"transitioning":null].filter(Boolean).join(" ");let $t=typeof d=="function"?d(Tt):d;return O.createElement(is,{...m,"aria-current":et,className:dt,ref:E,style:$t,to:b,viewTransition:x},typeof p=="function"?p(Tt):p)});lv.displayName="NavLink";var nv=O.forwardRef(({discover:r="render",fetcherKey:u,navigate:c,reloadDocument:s,replace:o,state:d,method:b=ci,action:x,onSubmit:p,relative:m,preventScrollReset:E,viewTransition:C,...D},Y)=>{let U=cv(),L=sv(x,{relative:m}),V=b.toLowerCase()==="get"?"get":"post",B=typeof x=="string"&&Zd.test(x),k=Q=>{if(p&&p(Q),Q.defaultPrevented)return;Q.preventDefault();let P=Q.nativeEvent.submitter,J=P?.getAttribute("formmethod")||b;U(P||Q.currentTarget,{fetcherKey:u,method:J,navigate:c,replace:o,state:d,relative:m,preventScrollReset:E,viewTransition:C})};return O.createElement("form",{ref:Y,method:V,action:L,onSubmit:s?p:k,...D,"data-discover":!B&&r==="render"?"true":void 0})});nv.displayName="Form";function av(r){return`${r} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function kd(r){let u=O.useContext(Bn);return Ot(u,av(r)),u}function uv(r,{target:u,replace:c,state:s,preventScrollReset:o,relative:d,viewTransition:b}={}){let x=m0(),p=Kl(),m=Ga(r,{relative:d});return O.useCallback(E=>{if(H0(E,u)){E.preventDefault();let C=c!==void 0?c:Ya(p)===Ya(m);x(r,{replace:C,state:s,preventScrollReset:o,relative:d,viewTransition:b})}},[p,x,m,c,s,u,r,o,d,b])}var iv=0,rv=()=>`__${String(++iv)}__`;function cv(){let{router:r}=kd("useSubmit"),{basename:u}=O.useContext(He),c=R0();return O.useCallback(async(s,o={})=>{let{action:d,method:b,encType:x,formData:p,body:m}=j0(s,u);if(o.navigate===!1){let E=o.fetcherKey||rv();await r.fetch(E,c,o.action||d,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||b,formEncType:o.encType||x,flushSync:o.flushSync})}else await r.navigate(o.action||d,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||b,formEncType:o.encType||x,replace:o.replace,state:o.state,fromRouteId:c,flushSync:o.flushSync,viewTransition:o.viewTransition})},[r,u,c])}function sv(r,{relative:u}={}){let{basename:c}=O.useContext(He),s=O.useContext(qe);Ot(s,"useFormAction must be used inside a RouteContext");let[o]=s.matches.slice(-1),d={...Ga(r||".",{relative:u})},b=Kl();if(r==null){d.search=b.search;let x=new URLSearchParams(d.search),p=x.getAll("index");if(p.some(E=>E==="")){x.delete("index"),p.filter(C=>C).forEach(C=>x.append("index",C));let E=x.toString();d.search=E?`?${E}`:""}}return(!r||r===".")&&o.route.index&&(d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index"),c!=="/"&&(d.pathname=d.pathname==="/"?c:Ie([c,d.pathname])),Ya(d)}function fv(r,u={}){let c=O.useContext(qd);Ot(c!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:s}=kd("useViewTransitionState"),o=Ga(r,{relative:u.relative});if(!c.isTransitioning)return!1;let d=tl(c.currentLocation.pathname,s)||c.currentLocation.pathname,b=tl(c.nextLocation.pathname,s)||c.nextLocation.pathname;return mi(o.pathname,b)!=null||mi(o.pathname,d)!=null}[...K0];const ov=()=>{const r=[{id:"1",name:"Demo Chat",lastMessage:"Click to start chatting!",timestamp:new Date().toLocaleTimeString(),unread:0}];return q.jsxs("div",{className:"flex flex-col h-screen bg-white",children:[q.jsxs("div",{className:"bg-whatsapp-green text-white p-4",children:[q.jsx("h1",{className:"text-xl font-semibold",children:"EchoGram"}),q.jsx("p",{className:"text-sm opacity-90",children:"Voice-First Messenger"})]}),q.jsx("div",{className:"flex-1 overflow-y-auto",children:r.map(u=>q.jsxs(is,{to:`/chat/${u.id}`,className:"flex items-center p-4 hover:bg-gray-50 border-b border-gray-100",children:[q.jsx("div",{className:"w-12 h-12 bg-whatsapp-green rounded-full flex items-center justify-center mr-3",children:q.jsx("span",{className:"text-white font-medium text-lg",children:u.name.charAt(0)})}),q.jsxs("div",{className:"flex-1 min-w-0",children:[q.jsxs("div",{className:"flex items-center justify-between",children:[q.jsx("h3",{className:"text-text-primary font-medium truncate",children:u.name}),q.jsx("span",{className:"text-text-secondary text-xs",children:u.timestamp})]}),q.jsxs("div",{className:"flex items-center justify-between mt-1",children:[q.jsx("p",{className:"text-text-secondary text-sm truncate",children:u.lastMessage}),u.unread>0&&q.jsx("span",{className:"bg-whatsapp-green text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center",children:u.unread})]})]})]},u.id))}),q.jsx("div",{className:"p-4",children:q.jsx("button",{className:"w-full bg-whatsapp-green text-white py-3 rounded-lg font-medium hover:bg-whatsapp-green-light transition-colors",children:"Start New Chat"})})]})},hv=({audioUrl:r,duration:u=0,isOwn:c=!1,autoPlay:s=!1})=>{const[o,d]=O.useState(!1),[b,x]=O.useState(0),[p,m]=O.useState(u),E=O.useRef(null);O.useEffect(()=>{s&&r&&C()},[s,r]);const C=()=>{E.current&&(E.current.play(),d(!0))},D=()=>{E.current&&(E.current.pause(),d(!1))},Y=()=>{E.current&&x(E.current.currentTime)},U=()=>{E.current&&m(E.current.duration)},L=()=>{d(!1),x(0)},V=Q=>{const P=Math.floor(Q/60),J=Math.floor(Q%60);return`${P}:${J.toString().padStart(2,"0")}`},B=p>0?b/p*100:0,k=()=>{const Q=[];for(let P=0;P<20;P++){const J=Math.random()*16+4,_t=P<B/5;Q.push(q.jsx("div",{className:`w-1 rounded-full transition-colors duration-150 ${_t?"bg-whatsapp-green":"bg-waveform-inactive"} ${o&&!_t?"waveform-bar":""}`,style:{height:`${J}px`}},P))}return Q};return q.jsxs("div",{className:`flex items-center space-x-3 p-3 rounded-lg max-w-xs ${c?"bg-whatsapp-green text-white":"bg-white border border-gray-200"}`,children:[q.jsx("audio",{ref:E,src:r,onTimeUpdate:Y,onLoadedMetadata:U,onEnded:L}),q.jsx("button",{onClick:o?D:C,className:`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-colors ${c?"bg-white bg-opacity-20 hover:bg-opacity-30":"bg-whatsapp-green hover:bg-whatsapp-green-light"}`,children:o?q.jsxs("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 24 24",children:[q.jsx("rect",{x:"6",y:"4",width:"4",height:"16"}),q.jsx("rect",{x:"14",y:"4",width:"4",height:"16"})]}):q.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 24 24",children:q.jsx("polygon",{points:"5,3 19,12 5,21"})})}),q.jsx("div",{className:"flex items-center space-x-1 flex-1",children:k()}),q.jsxs("span",{className:`text-xs font-medium ${c?"text-white text-opacity-80":"text-text-secondary"}`,children:[V(b)," / ",V(p)]})]})},dv=({message:r,isOwn:u,autoPlay:c=!1})=>{const s=o=>new Date(o).toLocaleTimeString("en-US",{hour12:!0,hour:"numeric",minute:"2-digit"});return q.jsxs("div",{className:`flex ${u?"justify-end":"justify-start"} mb-4`,children:[q.jsxs("div",{className:`max-w-xs lg:max-w-md ${u?"order-2":"order-1"}`,children:[r.messageType==="voice"?q.jsx(hv,{audioUrl:`http://localhost:5000${r.audioFile?.url}`,duration:r.audioFile?.duration,isOwn:u,autoPlay:c}):q.jsx("div",{className:`px-4 py-2 rounded-lg ${u?"bg-whatsapp-green text-white rounded-br-none":"bg-white border border-gray-200 rounded-bl-none"}`,children:q.jsx("p",{className:"text-sm",children:r.content})}),q.jsxs("div",{className:`flex items-center mt-1 space-x-1 ${u?"justify-end":"justify-start"}`,children:[q.jsx("span",{className:"text-xs text-text-secondary",children:s(r.createdAt)}),u&&q.jsxs("div",{className:"flex space-x-0.5",children:[q.jsx("svg",{className:"w-3 h-3 text-text-secondary",fill:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{d:"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"})}),q.jsx("svg",{className:"w-3 h-3 text-text-secondary -ml-1",fill:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{d:"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"})})]})]})]}),!u&&q.jsx("div",{className:"w-8 h-8 rounded-full bg-whatsapp-green flex items-center justify-center ml-2 order-2 flex-shrink-0",children:q.jsx("span",{className:"text-white text-xs font-medium",children:r.sender?.username?.charAt(0).toUpperCase()})})]})},xd=r=>{let u;const c=new Set,s=(m,E)=>{const C=typeof m=="function"?m(u):m;if(!Object.is(C,u)){const D=u;u=E??(typeof C!="object"||C===null)?C:Object.assign({},u,C),c.forEach(Y=>Y(u,D))}},o=()=>u,x={setState:s,getState:o,getInitialState:()=>p,subscribe:m=>(c.add(m),()=>c.delete(m))},p=u=r(s,o,x);return x},mv=r=>r?xd(r):xd,yv=r=>r;function pv(r,u=yv){const c=od.useSyncExternalStore(r.subscribe,()=>u(r.getState()),()=>u(r.getInitialState()));return od.useDebugValue(c),c}const Ad=r=>{const u=mv(r),c=s=>pv(u,s);return Object.assign(c,u),c},vv=r=>r?Ad(r):Ad,rs=vv((r,u)=>({user:null,token:localStorage.getItem("token"),isAuthenticated:!1,isLoading:!1,login:async(c,s)=>{r({isLoading:!0});try{const o=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:c,password:s})}),d=await o.json();return o.ok?(localStorage.setItem("token",d.token),r({user:d.user,token:d.token,isAuthenticated:!0,isLoading:!1}),{success:!0}):(r({isLoading:!1}),{success:!1,message:d.message})}catch{return r({isLoading:!1}),{success:!1,message:"Network error"}}},register:async(c,s,o)=>{r({isLoading:!0});try{const d=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:c,email:s,password:o})}),b=await d.json();return d.ok?(localStorage.setItem("token",b.token),r({user:b.user,token:b.token,isAuthenticated:!0,isLoading:!1}),{success:!0}):(r({isLoading:!1}),{success:!1,message:b.message})}catch{return r({isLoading:!1}),{success:!1,message:"Network error"}}},logout:()=>{localStorage.removeItem("token"),r({user:null,token:null,isAuthenticated:!1})},checkAuth:async()=>{const c=u().token;if(c)try{const s=await fetch("/api/auth/me",{headers:{Authorization:`Bearer ${c}`}});if(s.ok){const o=await s.json();r({user:o.user,isAuthenticated:!0})}else u().logout()}catch{u().logout()}},updateUser:c=>{r(s=>({user:s.user?{...s.user,...c}:null}))}})),gv=()=>{const[r,u]=O.useState(!1),[c,s]=O.useState(0),[o,d]=O.useState(null),[b,x]=O.useState(null),p=O.useRef(null),m=O.useRef(null),E=O.useRef([]),C=O.useRef(null),{token:D}=rs(),Y=async()=>{try{const k=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}});m.current=k,E.current=[];const Q=new MediaRecorder(k,{mimeType:"audio/webm;codecs=opus"});p.current=Q,Q.ondataavailable=P=>{P.data.size>0&&E.current.push(P.data)},Q.onstop=()=>{const P=new Blob(E.current,{type:"audio/webm"});d(P),x(URL.createObjectURL(P)),m.current?.getTracks().forEach(J=>J.stop())},Q.start(),u(!0),s(0),C.current=setInterval(()=>{s(P=>P+1)},1e3)}catch(k){console.error("Error starting recording:",k),alert("Could not access microphone. Please check permissions.")}},U=()=>{p.current&&r&&(p.current.stop(),u(!1),C.current&&(clearInterval(C.current),C.current=null))},L=async()=>{if(!o||!D)return null;const k=new FormData;k.append("audio",o,"voice-note.webm");try{const Q=await fetch("http://localhost:5000/api/upload/audio",{method:"POST",headers:{Authorization:`Bearer ${D}`},body:k});if(Q.ok)return(await Q.json()).audioFile;throw new Error("Upload failed")}catch(Q){return console.error("Error uploading audio:",Q),null}},V=()=>{d(null),x(null),s(0),E.current=[]};return{isRecording:r,recordingDuration:(k=>{const Q=Math.floor(k/60),P=k%60;return`${Q}:${P.toString().padStart(2,"0")}`})(c),audioBlob:o,audioUrl:b,startRecording:Y,stopRecording:U,uploadAudio:L,resetRecording:V}},Be=Object.create(null);Be.open="0";Be.close="1";Be.ping="2";Be.pong="3";Be.message="4";Be.upgrade="5";Be.noop="6";const fi=Object.create(null);Object.keys(Be).forEach(r=>{fi[Be[r]]=r});const Jc={type:"error",data:"parser error"},Kd=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",Jd=typeof ArrayBuffer=="function",$d=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r&&r.buffer instanceof ArrayBuffer,cs=({type:r,data:u},c,s)=>Kd&&u instanceof Blob?c?s(u):Rd(u,s):Jd&&(u instanceof ArrayBuffer||$d(u))?c?s(u):Rd(new Blob([u]),s):s(Be[r]+(u||"")),Rd=(r,u)=>{const c=new FileReader;return c.onload=function(){const s=c.result.split(",")[1];u("b"+(s||""))},c.readAsDataURL(r)};function Od(r){return r instanceof Uint8Array?r:r instanceof ArrayBuffer?new Uint8Array(r):new Uint8Array(r.buffer,r.byteOffset,r.byteLength)}let Vc;function bv(r,u){if(Kd&&r.data instanceof Blob)return r.data.arrayBuffer().then(Od).then(u);if(Jd&&(r.data instanceof ArrayBuffer||$d(r.data)))return u(Od(r.data));cs(r,!1,c=>{Vc||(Vc=new TextEncoder),u(Vc.encode(c))})}const Nd="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ja=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let r=0;r<Nd.length;r++)ja[Nd.charCodeAt(r)]=r;const Sv=r=>{let u=r.length*.75,c=r.length,s,o=0,d,b,x,p;r[r.length-1]==="="&&(u--,r[r.length-2]==="="&&u--);const m=new ArrayBuffer(u),E=new Uint8Array(m);for(s=0;s<c;s+=4)d=ja[r.charCodeAt(s)],b=ja[r.charCodeAt(s+1)],x=ja[r.charCodeAt(s+2)],p=ja[r.charCodeAt(s+3)],E[o++]=d<<2|b>>4,E[o++]=(b&15)<<4|x>>2,E[o++]=(x&3)<<6|p&63;return m},Ev=typeof ArrayBuffer=="function",ss=(r,u)=>{if(typeof r!="string")return{type:"message",data:Wd(r,u)};const c=r.charAt(0);return c==="b"?{type:"message",data:_v(r.substring(1),u)}:fi[c]?r.length>1?{type:fi[c],data:r.substring(1)}:{type:fi[c]}:Jc},_v=(r,u)=>{if(Ev){const c=Sv(r);return Wd(c,u)}else return{base64:!0,data:r}},Wd=(r,u)=>{switch(u){case"blob":return r instanceof Blob?r:new Blob([r]);case"arraybuffer":default:return r instanceof ArrayBuffer?r:r.buffer}},Fd="",Tv=(r,u)=>{const c=r.length,s=new Array(c);let o=0;r.forEach((d,b)=>{cs(d,!1,x=>{s[b]=x,++o===c&&u(s.join(Fd))})})},xv=(r,u)=>{const c=r.split(Fd),s=[];for(let o=0;o<c.length;o++){const d=ss(c[o],u);if(s.push(d),d.type==="error")break}return s};function Av(){return new TransformStream({transform(r,u){bv(r,c=>{const s=c.length;let o;if(s<126)o=new Uint8Array(1),new DataView(o.buffer).setUint8(0,s);else if(s<65536){o=new Uint8Array(3);const d=new DataView(o.buffer);d.setUint8(0,126),d.setUint16(1,s)}else{o=new Uint8Array(9);const d=new DataView(o.buffer);d.setUint8(0,127),d.setBigUint64(1,BigInt(s))}r.data&&typeof r.data!="string"&&(o[0]|=128),u.enqueue(o),u.enqueue(c)})}})}let Gc;function ii(r){return r.reduce((u,c)=>u+c.length,0)}function ri(r,u){if(r[0].length===u)return r.shift();const c=new Uint8Array(u);let s=0;for(let o=0;o<u;o++)c[o]=r[0][s++],s===r[0].length&&(r.shift(),s=0);return r.length&&s<r[0].length&&(r[0]=r[0].slice(s)),c}function Rv(r,u){Gc||(Gc=new TextDecoder);const c=[];let s=0,o=-1,d=!1;return new TransformStream({transform(b,x){for(c.push(b);;){if(s===0){if(ii(c)<1)break;const p=ri(c,1);d=(p[0]&128)===128,o=p[0]&127,o<126?s=3:o===126?s=1:s=2}else if(s===1){if(ii(c)<2)break;const p=ri(c,2);o=new DataView(p.buffer,p.byteOffset,p.length).getUint16(0),s=3}else if(s===2){if(ii(c)<8)break;const p=ri(c,8),m=new DataView(p.buffer,p.byteOffset,p.length),E=m.getUint32(0);if(E>Math.pow(2,21)-1){x.enqueue(Jc);break}o=E*Math.pow(2,32)+m.getUint32(4),s=3}else{if(ii(c)<o)break;const p=ri(c,o);x.enqueue(ss(d?p:Gc.decode(p),u)),s=0}if(o===0||o>r){x.enqueue(Jc);break}}}})}const Pd=4;function Ut(r){if(r)return Ov(r)}function Ov(r){for(var u in Ut.prototype)r[u]=Ut.prototype[u];return r}Ut.prototype.on=Ut.prototype.addEventListener=function(r,u){return this._callbacks=this._callbacks||{},(this._callbacks["$"+r]=this._callbacks["$"+r]||[]).push(u),this};Ut.prototype.once=function(r,u){function c(){this.off(r,c),u.apply(this,arguments)}return c.fn=u,this.on(r,c),this};Ut.prototype.off=Ut.prototype.removeListener=Ut.prototype.removeAllListeners=Ut.prototype.removeEventListener=function(r,u){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var c=this._callbacks["$"+r];if(!c)return this;if(arguments.length==1)return delete this._callbacks["$"+r],this;for(var s,o=0;o<c.length;o++)if(s=c[o],s===u||s.fn===u){c.splice(o,1);break}return c.length===0&&delete this._callbacks["$"+r],this};Ut.prototype.emit=function(r){this._callbacks=this._callbacks||{};for(var u=new Array(arguments.length-1),c=this._callbacks["$"+r],s=1;s<arguments.length;s++)u[s-1]=arguments[s];if(c){c=c.slice(0);for(var s=0,o=c.length;s<o;++s)c[s].apply(this,u)}return this};Ut.prototype.emitReserved=Ut.prototype.emit;Ut.prototype.listeners=function(r){return this._callbacks=this._callbacks||{},this._callbacks["$"+r]||[]};Ut.prototype.hasListeners=function(r){return!!this.listeners(r).length};const vi=typeof Promise=="function"&&typeof Promise.resolve=="function"?u=>Promise.resolve().then(u):(u,c)=>c(u,0),_e=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),Nv="arraybuffer";function Id(r,...u){return u.reduce((c,s)=>(r.hasOwnProperty(s)&&(c[s]=r[s]),c),{})}const Mv=_e.setTimeout,Dv=_e.clearTimeout;function gi(r,u){u.useNativeTimers?(r.setTimeoutFn=Mv.bind(_e),r.clearTimeoutFn=Dv.bind(_e)):(r.setTimeoutFn=_e.setTimeout.bind(_e),r.clearTimeoutFn=_e.clearTimeout.bind(_e))}const wv=1.33;function zv(r){return typeof r=="string"?Cv(r):Math.ceil((r.byteLength||r.size)*wv)}function Cv(r){let u=0,c=0;for(let s=0,o=r.length;s<o;s++)u=r.charCodeAt(s),u<128?c+=1:u<2048?c+=2:u<55296||u>=57344?c+=3:(s++,c+=4);return c}function tm(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function Uv(r){let u="";for(let c in r)r.hasOwnProperty(c)&&(u.length&&(u+="&"),u+=encodeURIComponent(c)+"="+encodeURIComponent(r[c]));return u}function Bv(r){let u={},c=r.split("&");for(let s=0,o=c.length;s<o;s++){let d=c[s].split("=");u[decodeURIComponent(d[0])]=decodeURIComponent(d[1])}return u}class Hv extends Error{constructor(u,c,s){super(u),this.description=c,this.context=s,this.type="TransportError"}}class fs extends Ut{constructor(u){super(),this.writable=!1,gi(this,u),this.opts=u,this.query=u.query,this.socket=u.socket,this.supportsBinary=!u.forceBase64}onError(u,c,s){return super.emitReserved("error",new Hv(u,c,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(u){this.readyState==="open"&&this.write(u)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(u){const c=ss(u,this.socket.binaryType);this.onPacket(c)}onPacket(u){super.emitReserved("packet",u)}onClose(u){this.readyState="closed",super.emitReserved("close",u)}pause(u){}createUri(u,c={}){return u+"://"+this._hostname()+this._port()+this.opts.path+this._query(c)}_hostname(){const u=this.opts.hostname;return u.indexOf(":")===-1?u:"["+u+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(u){const c=Uv(u);return c.length?"?"+c:""}}class qv extends fs{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(u){this.readyState="pausing";const c=()=>{this.readyState="paused",u()};if(this._polling||!this.writable){let s=0;this._polling&&(s++,this.once("pollComplete",function(){--s||c()})),this.writable||(s++,this.once("drain",function(){--s||c()}))}else c()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(u){const c=s=>{if(this.readyState==="opening"&&s.type==="open"&&this.onOpen(),s.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(s)};xv(u,this.socket.binaryType).forEach(c),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const u=()=>{this.write([{type:"close"}])};this.readyState==="open"?u():this.once("open",u)}write(u){this.writable=!1,Tv(u,c=>{this.doWrite(c,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const u=this.opts.secure?"https":"http",c=this.query||{};return this.opts.timestampRequests!==!1&&(c[this.opts.timestampParam]=tm()),!this.supportsBinary&&!c.sid&&(c.b64=1),this.createUri(u,c)}}let em=!1;try{em=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const Lv=em;function jv(){}class Yv extends qv{constructor(u){if(super(u),typeof location<"u"){const c=location.protocol==="https:";let s=location.port;s||(s=c?"443":"80"),this.xd=typeof location<"u"&&u.hostname!==location.hostname||s!==u.port}}doWrite(u,c){const s=this.request({method:"POST",data:u});s.on("success",c),s.on("error",(o,d)=>{this.onError("xhr post error",o,d)})}doPoll(){const u=this.request();u.on("data",this.onData.bind(this)),u.on("error",(c,s)=>{this.onError("xhr poll error",c,s)}),this.pollXhr=u}}class Ce extends Ut{constructor(u,c,s){super(),this.createRequest=u,gi(this,s),this._opts=s,this._method=s.method||"GET",this._uri=c,this._data=s.data!==void 0?s.data:null,this._create()}_create(){var u;const c=Id(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");c.xdomain=!!this._opts.xd;const s=this._xhr=this.createRequest(c);try{s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0);for(let o in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(o)&&s.setRequestHeader(o,this._opts.extraHeaders[o])}}catch{}if(this._method==="POST")try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{s.setRequestHeader("Accept","*/*")}catch{}(u=this._opts.cookieJar)===null||u===void 0||u.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var o;s.readyState===3&&((o=this._opts.cookieJar)===null||o===void 0||o.parseCookies(s.getResponseHeader("set-cookie"))),s.readyState===4&&(s.status===200||s.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof s.status=="number"?s.status:0)},0))},s.send(this._data)}catch(o){this.setTimeoutFn(()=>{this._onError(o)},0);return}typeof document<"u"&&(this._index=Ce.requestsCount++,Ce.requests[this._index]=this)}_onError(u){this.emitReserved("error",u,this._xhr),this._cleanup(!0)}_cleanup(u){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=jv,u)try{this._xhr.abort()}catch{}typeof document<"u"&&delete Ce.requests[this._index],this._xhr=null}}_onLoad(){const u=this._xhr.responseText;u!==null&&(this.emitReserved("data",u),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}Ce.requestsCount=0;Ce.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Md);else if(typeof addEventListener=="function"){const r="onpagehide"in _e?"pagehide":"unload";addEventListener(r,Md,!1)}}function Md(){for(let r in Ce.requests)Ce.requests.hasOwnProperty(r)&&Ce.requests[r].abort()}const Xv=function(){const r=lm({xdomain:!1});return r&&r.responseType!==null}();class Vv extends Yv{constructor(u){super(u);const c=u&&u.forceBase64;this.supportsBinary=Xv&&!c}request(u={}){return Object.assign(u,{xd:this.xd},this.opts),new Ce(lm,this.uri(),u)}}function lm(r){const u=r.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!u||Lv))return new XMLHttpRequest}catch{}if(!u)try{return new _e[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const nm=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Gv extends fs{get name(){return"websocket"}doOpen(){const u=this.uri(),c=this.opts.protocols,s=nm?{}:Id(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(u,c,s)}catch(o){return this.emitReserved("error",o)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=u=>this.onClose({description:"websocket connection closed",context:u}),this.ws.onmessage=u=>this.onData(u.data),this.ws.onerror=u=>this.onError("websocket error",u)}write(u){this.writable=!1;for(let c=0;c<u.length;c++){const s=u[c],o=c===u.length-1;cs(s,this.supportsBinary,d=>{try{this.doWrite(s,d)}catch{}o&&vi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const u=this.opts.secure?"wss":"ws",c=this.query||{};return this.opts.timestampRequests&&(c[this.opts.timestampParam]=tm()),this.supportsBinary||(c.b64=1),this.createUri(u,c)}}const Qc=_e.WebSocket||_e.MozWebSocket;class Qv extends Gv{createSocket(u,c,s){return nm?new Qc(u,c,s):c?new Qc(u,c):new Qc(u)}doWrite(u,c){this.ws.send(c)}}class Zv extends fs{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(u){return this.emitReserved("error",u)}this._transport.closed.then(()=>{this.onClose()}).catch(u=>{this.onError("webtransport error",u)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(u=>{const c=Rv(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=u.readable.pipeThrough(c).getReader(),o=Av();o.readable.pipeTo(u.writable),this._writer=o.writable.getWriter();const d=()=>{s.read().then(({done:x,value:p})=>{x||(this.onPacket(p),d())}).catch(x=>{})};d();const b={type:"open"};this.query.sid&&(b.data=`{"sid":"${this.query.sid}"}`),this._writer.write(b).then(()=>this.onOpen())})})}write(u){this.writable=!1;for(let c=0;c<u.length;c++){const s=u[c],o=c===u.length-1;this._writer.write(s).then(()=>{o&&vi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var u;(u=this._transport)===null||u===void 0||u.close()}}const kv={websocket:Qv,webtransport:Zv,polling:Vv},Kv=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Jv=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function $c(r){if(r.length>8e3)throw"URI too long";const u=r,c=r.indexOf("["),s=r.indexOf("]");c!=-1&&s!=-1&&(r=r.substring(0,c)+r.substring(c,s).replace(/:/g,";")+r.substring(s,r.length));let o=Kv.exec(r||""),d={},b=14;for(;b--;)d[Jv[b]]=o[b]||"";return c!=-1&&s!=-1&&(d.source=u,d.host=d.host.substring(1,d.host.length-1).replace(/;/g,":"),d.authority=d.authority.replace("[","").replace("]","").replace(/;/g,":"),d.ipv6uri=!0),d.pathNames=$v(d,d.path),d.queryKey=Wv(d,d.query),d}function $v(r,u){const c=/\/{2,9}/g,s=u.replace(c,"/").split("/");return(u.slice(0,1)=="/"||u.length===0)&&s.splice(0,1),u.slice(-1)=="/"&&s.splice(s.length-1,1),s}function Wv(r,u){const c={};return u.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(s,o,d){o&&(c[o]=d)}),c}const Wc=typeof addEventListener=="function"&&typeof removeEventListener=="function",oi=[];Wc&&addEventListener("offline",()=>{oi.forEach(r=>r())},!1);class Rl extends Ut{constructor(u,c){if(super(),this.binaryType=Nv,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,u&&typeof u=="object"&&(c=u,u=null),u){const s=$c(u);c.hostname=s.host,c.secure=s.protocol==="https"||s.protocol==="wss",c.port=s.port,s.query&&(c.query=s.query)}else c.host&&(c.hostname=$c(c.host).host);gi(this,c),this.secure=c.secure!=null?c.secure:typeof location<"u"&&location.protocol==="https:",c.hostname&&!c.port&&(c.port=this.secure?"443":"80"),this.hostname=c.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=c.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},c.transports.forEach(s=>{const o=s.prototype.name;this.transports.push(o),this._transportsByName[o]=s}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},c),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Bv(this.opts.query)),Wc&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},oi.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(u){const c=Object.assign({},this.opts.query);c.EIO=Pd,c.transport=u,this.id&&(c.sid=this.id);const s=Object.assign({},this.opts,{query:c,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[u]);return new this._transportsByName[u](s)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const u=this.opts.rememberUpgrade&&Rl.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const c=this.createTransport(u);c.open(),this.setTransport(c)}setTransport(u){this.transport&&this.transport.removeAllListeners(),this.transport=u,u.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",c=>this._onClose("transport close",c))}onOpen(){this.readyState="open",Rl.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(u){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",u),this.emitReserved("heartbeat"),u.type){case"open":this.onHandshake(JSON.parse(u.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const c=new Error("server error");c.code=u.data,this._onError(c);break;case"message":this.emitReserved("data",u.data),this.emitReserved("message",u.data);break}}onHandshake(u){this.emitReserved("handshake",u),this.id=u.sid,this.transport.query.sid=u.sid,this._pingInterval=u.pingInterval,this._pingTimeout=u.pingTimeout,this._maxPayload=u.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const u=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+u,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},u),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const u=this._getWritablePackets();this.transport.send(u),this._prevBufferLen=u.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let c=1;for(let s=0;s<this.writeBuffer.length;s++){const o=this.writeBuffer[s].data;if(o&&(c+=zv(o)),s>0&&c>this._maxPayload)return this.writeBuffer.slice(0,s);c+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const u=Date.now()>this._pingTimeoutTime;return u&&(this._pingTimeoutTime=0,vi(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),u}write(u,c,s){return this._sendPacket("message",u,c,s),this}send(u,c,s){return this._sendPacket("message",u,c,s),this}_sendPacket(u,c,s,o){if(typeof c=="function"&&(o=c,c=void 0),typeof s=="function"&&(o=s,s=null),this.readyState==="closing"||this.readyState==="closed")return;s=s||{},s.compress=s.compress!==!1;const d={type:u,data:c,options:s};this.emitReserved("packetCreate",d),this.writeBuffer.push(d),o&&this.once("flush",o),this.flush()}close(){const u=()=>{this._onClose("forced close"),this.transport.close()},c=()=>{this.off("upgrade",c),this.off("upgradeError",c),u()},s=()=>{this.once("upgrade",c),this.once("upgradeError",c)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():u()}):this.upgrading?s():u()),this}_onError(u){if(Rl.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",u),this._onClose("transport error",u)}_onClose(u,c){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Wc&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const s=oi.indexOf(this._offlineEventListener);s!==-1&&oi.splice(s,1)}this.readyState="closed",this.id=null,this.emitReserved("close",u,c),this.writeBuffer=[],this._prevBufferLen=0}}}Rl.protocol=Pd;class Fv extends Rl{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let u=0;u<this._upgrades.length;u++)this._probe(this._upgrades[u])}_probe(u){let c=this.createTransport(u),s=!1;Rl.priorWebsocketSuccess=!1;const o=()=>{s||(c.send([{type:"ping",data:"probe"}]),c.once("packet",C=>{if(!s)if(C.type==="pong"&&C.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",c),!c)return;Rl.priorWebsocketSuccess=c.name==="websocket",this.transport.pause(()=>{s||this.readyState!=="closed"&&(E(),this.setTransport(c),c.send([{type:"upgrade"}]),this.emitReserved("upgrade",c),c=null,this.upgrading=!1,this.flush())})}else{const D=new Error("probe error");D.transport=c.name,this.emitReserved("upgradeError",D)}}))};function d(){s||(s=!0,E(),c.close(),c=null)}const b=C=>{const D=new Error("probe error: "+C);D.transport=c.name,d(),this.emitReserved("upgradeError",D)};function x(){b("transport closed")}function p(){b("socket closed")}function m(C){c&&C.name!==c.name&&d()}const E=()=>{c.removeListener("open",o),c.removeListener("error",b),c.removeListener("close",x),this.off("close",p),this.off("upgrading",m)};c.once("open",o),c.once("error",b),c.once("close",x),this.once("close",p),this.once("upgrading",m),this._upgrades.indexOf("webtransport")!==-1&&u!=="webtransport"?this.setTimeoutFn(()=>{s||c.open()},200):c.open()}onHandshake(u){this._upgrades=this._filterUpgrades(u.upgrades),super.onHandshake(u)}_filterUpgrades(u){const c=[];for(let s=0;s<u.length;s++)~this.transports.indexOf(u[s])&&c.push(u[s]);return c}}let Pv=class extends Fv{constructor(u,c={}){const s=typeof u=="object"?u:c;(!s.transports||s.transports&&typeof s.transports[0]=="string")&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(o=>kv[o]).filter(o=>!!o)),super(u,s)}};function Iv(r,u="",c){let s=r;c=c||typeof location<"u"&&location,r==null&&(r=c.protocol+"//"+c.host),typeof r=="string"&&(r.charAt(0)==="/"&&(r.charAt(1)==="/"?r=c.protocol+r:r=c.host+r),/^(https?|wss?):\/\//.test(r)||(typeof c<"u"?r=c.protocol+"//"+r:r="https://"+r),s=$c(r)),s.port||(/^(http|ws)$/.test(s.protocol)?s.port="80":/^(http|ws)s$/.test(s.protocol)&&(s.port="443")),s.path=s.path||"/";const d=s.host.indexOf(":")!==-1?"["+s.host+"]":s.host;return s.id=s.protocol+"://"+d+":"+s.port+u,s.href=s.protocol+"://"+d+(c&&c.port===s.port?"":":"+s.port),s}const tg=typeof ArrayBuffer=="function",eg=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r.buffer instanceof ArrayBuffer,am=Object.prototype.toString,lg=typeof Blob=="function"||typeof Blob<"u"&&am.call(Blob)==="[object BlobConstructor]",ng=typeof File=="function"||typeof File<"u"&&am.call(File)==="[object FileConstructor]";function os(r){return tg&&(r instanceof ArrayBuffer||eg(r))||lg&&r instanceof Blob||ng&&r instanceof File}function hi(r,u){if(!r||typeof r!="object")return!1;if(Array.isArray(r)){for(let c=0,s=r.length;c<s;c++)if(hi(r[c]))return!0;return!1}if(os(r))return!0;if(r.toJSON&&typeof r.toJSON=="function"&&arguments.length===1)return hi(r.toJSON(),!0);for(const c in r)if(Object.prototype.hasOwnProperty.call(r,c)&&hi(r[c]))return!0;return!1}function ag(r){const u=[],c=r.data,s=r;return s.data=Fc(c,u),s.attachments=u.length,{packet:s,buffers:u}}function Fc(r,u){if(!r)return r;if(os(r)){const c={_placeholder:!0,num:u.length};return u.push(r),c}else if(Array.isArray(r)){const c=new Array(r.length);for(let s=0;s<r.length;s++)c[s]=Fc(r[s],u);return c}else if(typeof r=="object"&&!(r instanceof Date)){const c={};for(const s in r)Object.prototype.hasOwnProperty.call(r,s)&&(c[s]=Fc(r[s],u));return c}return r}function ug(r,u){return r.data=Pc(r.data,u),delete r.attachments,r}function Pc(r,u){if(!r)return r;if(r&&r._placeholder===!0){if(typeof r.num=="number"&&r.num>=0&&r.num<u.length)return u[r.num];throw new Error("illegal attachments")}else if(Array.isArray(r))for(let c=0;c<r.length;c++)r[c]=Pc(r[c],u);else if(typeof r=="object")for(const c in r)Object.prototype.hasOwnProperty.call(r,c)&&(r[c]=Pc(r[c],u));return r}const ig=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],rg=5;var ct;(function(r){r[r.CONNECT=0]="CONNECT",r[r.DISCONNECT=1]="DISCONNECT",r[r.EVENT=2]="EVENT",r[r.ACK=3]="ACK",r[r.CONNECT_ERROR=4]="CONNECT_ERROR",r[r.BINARY_EVENT=5]="BINARY_EVENT",r[r.BINARY_ACK=6]="BINARY_ACK"})(ct||(ct={}));class cg{constructor(u){this.replacer=u}encode(u){return(u.type===ct.EVENT||u.type===ct.ACK)&&hi(u)?this.encodeAsBinary({type:u.type===ct.EVENT?ct.BINARY_EVENT:ct.BINARY_ACK,nsp:u.nsp,data:u.data,id:u.id}):[this.encodeAsString(u)]}encodeAsString(u){let c=""+u.type;return(u.type===ct.BINARY_EVENT||u.type===ct.BINARY_ACK)&&(c+=u.attachments+"-"),u.nsp&&u.nsp!=="/"&&(c+=u.nsp+","),u.id!=null&&(c+=u.id),u.data!=null&&(c+=JSON.stringify(u.data,this.replacer)),c}encodeAsBinary(u){const c=ag(u),s=this.encodeAsString(c.packet),o=c.buffers;return o.unshift(s),o}}function Dd(r){return Object.prototype.toString.call(r)==="[object Object]"}class hs extends Ut{constructor(u){super(),this.reviver=u}add(u){let c;if(typeof u=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");c=this.decodeString(u);const s=c.type===ct.BINARY_EVENT;s||c.type===ct.BINARY_ACK?(c.type=s?ct.EVENT:ct.ACK,this.reconstructor=new sg(c),c.attachments===0&&super.emitReserved("decoded",c)):super.emitReserved("decoded",c)}else if(os(u)||u.base64)if(this.reconstructor)c=this.reconstructor.takeBinaryData(u),c&&(this.reconstructor=null,super.emitReserved("decoded",c));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+u)}decodeString(u){let c=0;const s={type:Number(u.charAt(0))};if(ct[s.type]===void 0)throw new Error("unknown packet type "+s.type);if(s.type===ct.BINARY_EVENT||s.type===ct.BINARY_ACK){const d=c+1;for(;u.charAt(++c)!=="-"&&c!=u.length;);const b=u.substring(d,c);if(b!=Number(b)||u.charAt(c)!=="-")throw new Error("Illegal attachments");s.attachments=Number(b)}if(u.charAt(c+1)==="/"){const d=c+1;for(;++c&&!(u.charAt(c)===","||c===u.length););s.nsp=u.substring(d,c)}else s.nsp="/";const o=u.charAt(c+1);if(o!==""&&Number(o)==o){const d=c+1;for(;++c;){const b=u.charAt(c);if(b==null||Number(b)!=b){--c;break}if(c===u.length)break}s.id=Number(u.substring(d,c+1))}if(u.charAt(++c)){const d=this.tryParse(u.substr(c));if(hs.isPayloadValid(s.type,d))s.data=d;else throw new Error("invalid payload")}return s}tryParse(u){try{return JSON.parse(u,this.reviver)}catch{return!1}}static isPayloadValid(u,c){switch(u){case ct.CONNECT:return Dd(c);case ct.DISCONNECT:return c===void 0;case ct.CONNECT_ERROR:return typeof c=="string"||Dd(c);case ct.EVENT:case ct.BINARY_EVENT:return Array.isArray(c)&&(typeof c[0]=="number"||typeof c[0]=="string"&&ig.indexOf(c[0])===-1);case ct.ACK:case ct.BINARY_ACK:return Array.isArray(c)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class sg{constructor(u){this.packet=u,this.buffers=[],this.reconPack=u}takeBinaryData(u){if(this.buffers.push(u),this.buffers.length===this.reconPack.attachments){const c=ug(this.reconPack,this.buffers);return this.finishedReconstruction(),c}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const fg=Object.freeze(Object.defineProperty({__proto__:null,Decoder:hs,Encoder:cg,get PacketType(){return ct},protocol:rg},Symbol.toStringTag,{value:"Module"}));function Re(r,u,c){return r.on(u,c),function(){r.off(u,c)}}const og=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class um extends Ut{constructor(u,c,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=u,this.nsp=c,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const u=this.io;this.subs=[Re(u,"open",this.onopen.bind(this)),Re(u,"packet",this.onpacket.bind(this)),Re(u,"error",this.onerror.bind(this)),Re(u,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...u){return u.unshift("message"),this.emit.apply(this,u),this}emit(u,...c){var s,o,d;if(og.hasOwnProperty(u))throw new Error('"'+u.toString()+'" is a reserved event name');if(c.unshift(u),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(c),this;const b={type:ct.EVENT,data:c};if(b.options={},b.options.compress=this.flags.compress!==!1,typeof c[c.length-1]=="function"){const E=this.ids++,C=c.pop();this._registerAckCallback(E,C),b.id=E}const x=(o=(s=this.io.engine)===null||s===void 0?void 0:s.transport)===null||o===void 0?void 0:o.writable,p=this.connected&&!(!((d=this.io.engine)===null||d===void 0)&&d._hasPingExpired());return this.flags.volatile&&!x||(p?(this.notifyOutgoingListeners(b),this.packet(b)):this.sendBuffer.push(b)),this.flags={},this}_registerAckCallback(u,c){var s;const o=(s=this.flags.timeout)!==null&&s!==void 0?s:this._opts.ackTimeout;if(o===void 0){this.acks[u]=c;return}const d=this.io.setTimeoutFn(()=>{delete this.acks[u];for(let x=0;x<this.sendBuffer.length;x++)this.sendBuffer[x].id===u&&this.sendBuffer.splice(x,1);c.call(this,new Error("operation has timed out"))},o),b=(...x)=>{this.io.clearTimeoutFn(d),c.apply(this,x)};b.withError=!0,this.acks[u]=b}emitWithAck(u,...c){return new Promise((s,o)=>{const d=(b,x)=>b?o(b):s(x);d.withError=!0,c.push(d),this.emit(u,...c)})}_addToQueue(u){let c;typeof u[u.length-1]=="function"&&(c=u.pop());const s={id:this._queueSeq++,tryCount:0,pending:!1,args:u,flags:Object.assign({fromQueue:!0},this.flags)};u.push((o,...d)=>s!==this._queue[0]?void 0:(o!==null?s.tryCount>this._opts.retries&&(this._queue.shift(),c&&c(o)):(this._queue.shift(),c&&c(null,...d)),s.pending=!1,this._drainQueue())),this._queue.push(s),this._drainQueue()}_drainQueue(u=!1){if(!this.connected||this._queue.length===0)return;const c=this._queue[0];c.pending&&!u||(c.pending=!0,c.tryCount++,this.flags=c.flags,this.emit.apply(this,c.args))}packet(u){u.nsp=this.nsp,this.io._packet(u)}onopen(){typeof this.auth=="function"?this.auth(u=>{this._sendConnectPacket(u)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(u){this.packet({type:ct.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},u):u})}onerror(u){this.connected||this.emitReserved("connect_error",u)}onclose(u,c){this.connected=!1,delete this.id,this.emitReserved("disconnect",u,c),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(u=>{if(!this.sendBuffer.some(s=>String(s.id)===u)){const s=this.acks[u];delete this.acks[u],s.withError&&s.call(this,new Error("socket has been disconnected"))}})}onpacket(u){if(u.nsp===this.nsp)switch(u.type){case ct.CONNECT:u.data&&u.data.sid?this.onconnect(u.data.sid,u.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case ct.EVENT:case ct.BINARY_EVENT:this.onevent(u);break;case ct.ACK:case ct.BINARY_ACK:this.onack(u);break;case ct.DISCONNECT:this.ondisconnect();break;case ct.CONNECT_ERROR:this.destroy();const s=new Error(u.data.message);s.data=u.data.data,this.emitReserved("connect_error",s);break}}onevent(u){const c=u.data||[];u.id!=null&&c.push(this.ack(u.id)),this.connected?this.emitEvent(c):this.receiveBuffer.push(Object.freeze(c))}emitEvent(u){if(this._anyListeners&&this._anyListeners.length){const c=this._anyListeners.slice();for(const s of c)s.apply(this,u)}super.emit.apply(this,u),this._pid&&u.length&&typeof u[u.length-1]=="string"&&(this._lastOffset=u[u.length-1])}ack(u){const c=this;let s=!1;return function(...o){s||(s=!0,c.packet({type:ct.ACK,id:u,data:o}))}}onack(u){const c=this.acks[u.id];typeof c=="function"&&(delete this.acks[u.id],c.withError&&u.data.unshift(null),c.apply(this,u.data))}onconnect(u,c){this.id=u,this.recovered=c&&this._pid===c,this._pid=c,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(u=>this.emitEvent(u)),this.receiveBuffer=[],this.sendBuffer.forEach(u=>{this.notifyOutgoingListeners(u),this.packet(u)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(u=>u()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:ct.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(u){return this.flags.compress=u,this}get volatile(){return this.flags.volatile=!0,this}timeout(u){return this.flags.timeout=u,this}onAny(u){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(u),this}prependAny(u){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(u),this}offAny(u){if(!this._anyListeners)return this;if(u){const c=this._anyListeners;for(let s=0;s<c.length;s++)if(u===c[s])return c.splice(s,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(u){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(u),this}prependAnyOutgoing(u){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(u),this}offAnyOutgoing(u){if(!this._anyOutgoingListeners)return this;if(u){const c=this._anyOutgoingListeners;for(let s=0;s<c.length;s++)if(u===c[s])return c.splice(s,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(u){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const c=this._anyOutgoingListeners.slice();for(const s of c)s.apply(this,u.data)}}}function Hn(r){r=r||{},this.ms=r.min||100,this.max=r.max||1e4,this.factor=r.factor||2,this.jitter=r.jitter>0&&r.jitter<=1?r.jitter:0,this.attempts=0}Hn.prototype.duration=function(){var r=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var u=Math.random(),c=Math.floor(u*this.jitter*r);r=(Math.floor(u*10)&1)==0?r-c:r+c}return Math.min(r,this.max)|0};Hn.prototype.reset=function(){this.attempts=0};Hn.prototype.setMin=function(r){this.ms=r};Hn.prototype.setMax=function(r){this.max=r};Hn.prototype.setJitter=function(r){this.jitter=r};class Ic extends Ut{constructor(u,c){var s;super(),this.nsps={},this.subs=[],u&&typeof u=="object"&&(c=u,u=void 0),c=c||{},c.path=c.path||"/socket.io",this.opts=c,gi(this,c),this.reconnection(c.reconnection!==!1),this.reconnectionAttempts(c.reconnectionAttempts||1/0),this.reconnectionDelay(c.reconnectionDelay||1e3),this.reconnectionDelayMax(c.reconnectionDelayMax||5e3),this.randomizationFactor((s=c.randomizationFactor)!==null&&s!==void 0?s:.5),this.backoff=new Hn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(c.timeout==null?2e4:c.timeout),this._readyState="closed",this.uri=u;const o=c.parser||fg;this.encoder=new o.Encoder,this.decoder=new o.Decoder,this._autoConnect=c.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(u){return arguments.length?(this._reconnection=!!u,u||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(u){return u===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=u,this)}reconnectionDelay(u){var c;return u===void 0?this._reconnectionDelay:(this._reconnectionDelay=u,(c=this.backoff)===null||c===void 0||c.setMin(u),this)}randomizationFactor(u){var c;return u===void 0?this._randomizationFactor:(this._randomizationFactor=u,(c=this.backoff)===null||c===void 0||c.setJitter(u),this)}reconnectionDelayMax(u){var c;return u===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=u,(c=this.backoff)===null||c===void 0||c.setMax(u),this)}timeout(u){return arguments.length?(this._timeout=u,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(u){if(~this._readyState.indexOf("open"))return this;this.engine=new Pv(this.uri,this.opts);const c=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;const o=Re(c,"open",function(){s.onopen(),u&&u()}),d=x=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",x),u?u(x):this.maybeReconnectOnOpen()},b=Re(c,"error",d);if(this._timeout!==!1){const x=this._timeout,p=this.setTimeoutFn(()=>{o(),d(new Error("timeout")),c.close()},x);this.opts.autoUnref&&p.unref(),this.subs.push(()=>{this.clearTimeoutFn(p)})}return this.subs.push(o),this.subs.push(b),this}connect(u){return this.open(u)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const u=this.engine;this.subs.push(Re(u,"ping",this.onping.bind(this)),Re(u,"data",this.ondata.bind(this)),Re(u,"error",this.onerror.bind(this)),Re(u,"close",this.onclose.bind(this)),Re(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(u){try{this.decoder.add(u)}catch(c){this.onclose("parse error",c)}}ondecoded(u){vi(()=>{this.emitReserved("packet",u)},this.setTimeoutFn)}onerror(u){this.emitReserved("error",u)}socket(u,c){let s=this.nsps[u];return s?this._autoConnect&&!s.active&&s.connect():(s=new um(this,u,c),this.nsps[u]=s),s}_destroy(u){const c=Object.keys(this.nsps);for(const s of c)if(this.nsps[s].active)return;this._close()}_packet(u){const c=this.encoder.encode(u);for(let s=0;s<c.length;s++)this.engine.write(c[s],u.options)}cleanup(){this.subs.forEach(u=>u()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(u,c){var s;this.cleanup(),(s=this.engine)===null||s===void 0||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",u,c),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const u=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const c=this.backoff.duration();this._reconnecting=!0;const s=this.setTimeoutFn(()=>{u.skipReconnect||(this.emitReserved("reconnect_attempt",u.backoff.attempts),!u.skipReconnect&&u.open(o=>{o?(u._reconnecting=!1,u.reconnect(),this.emitReserved("reconnect_error",o)):u.onreconnect()}))},c);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){const u=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",u)}}const La={};function di(r,u){typeof r=="object"&&(u=r,r=void 0),u=u||{};const c=Iv(r,u.path||"/socket.io"),s=c.source,o=c.id,d=c.path,b=La[o]&&d in La[o].nsps,x=u.forceNew||u["force new connection"]||u.multiplex===!1||b;let p;return x?p=new Ic(s,u):(La[o]||(La[o]=new Ic(s,u)),p=La[o]),c.query&&!u.query&&(u.query=c.queryKey),p.socket(c.path,u)}Object.assign(di,{Manager:Ic,Socket:um,io:di,connect:di});const hg=()=>{const r=O.useRef(void 0),{token:u,isAuthenticated:c}=rs();return O.useEffect(()=>{if(c&&u)return r.current=di("http://localhost:5000",{transports:["websocket"]}),r.current.emit("authenticate",u),r.current.on("authenticated",s=>{console.log("Socket authenticated:",s)}),r.current.on("authentication_error",s=>{console.error("Socket authentication error:",s)}),()=>{r.current&&r.current.disconnect()}},[c,u]),r.current},dg=()=>{const{id:r}=p0(),[u,c]=O.useState([]),[s,o]=O.useState(""),[d,b]=O.useState(!1),x=O.useRef(null),{user:p,token:m}=rs(),E=hg(),{isRecording:C,recordingDuration:D,audioUrl:Y,startRecording:U,stopRecording:L,uploadAudio:V,resetRecording:B}=gv();O.useEffect(()=>{Q()},[u]),O.useEffect(()=>{if(E&&r)return E.emit("join_chat",r),k(),E.on("new_message",et=>{c(dt=>[...dt,et])}),E.on("auto_play_voice",et=>{console.log("Auto-play voice note:",et)}),()=>{E.off("new_message"),E.off("auto_play_voice")}},[E,r]);const k=async()=>{try{const et=await fetch(`http://localhost:5000/api/chats/${r}/messages`,{headers:{Authorization:`Bearer ${m}`}});if(et.ok){const dt=await et.json();c(dt)}}catch(et){console.error("Error loading messages:",et)}},Q=()=>{x.current?.scrollIntoView({behavior:"smooth"})},P=async()=>{if(!s.trim()||!E)return;const et={chatId:r,content:s,messageType:"text"};E.emit("send_message",et),o("")},J=async()=>{if(!Y||!E)return;const et=await V();if(et){const dt={chatId:r,messageType:"voice",audioFile:et};E.emit("send_message",dt),B()}},_t=et=>{et.key==="Enter"&&!et.shiftKey&&(et.preventDefault(),P())},Tt=()=>{E&&(E.emit("toggle_walkie_talkie",r),b(!d))};return q.jsxs("div",{className:"flex flex-col h-screen bg-chat-bg",children:[q.jsxs("div",{className:"bg-white border-b border-gray-200 p-4 flex items-center justify-between",children:[q.jsxs("div",{className:"flex items-center space-x-3",children:[q.jsx("button",{className:"p-2 hover:bg-gray-100 rounded-full",children:q.jsx("svg",{className:"w-5 h-5 text-text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),q.jsx("div",{className:"w-10 h-10 bg-whatsapp-green rounded-full flex items-center justify-center",children:q.jsx("span",{className:"text-white font-medium",children:"A"})}),q.jsxs("div",{children:[q.jsx("h1",{className:"font-semibold text-text-primary",children:"Chat"}),q.jsx("p",{className:"text-sm text-text-secondary",children:"Online"})]})]}),q.jsxs("div",{className:"flex items-center space-x-2",children:[q.jsx("button",{onClick:Tt,className:`p-2 rounded-full transition-colors ${d?"bg-whatsapp-green text-white":"hover:bg-gray-100 text-text-primary"}`,title:"Toggle Walkie-Talkie Mode",children:q.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{d:"M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"})})}),q.jsx("button",{className:"p-2 hover:bg-gray-100 rounded-full",children:q.jsx("svg",{className:"w-5 h-5 text-text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"})})})]})]}),q.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:[u.map(et=>q.jsx(dv,{message:et,isOwn:et.sender._id===p?.id,autoPlay:d&&et.messageType==="voice"},et._id)),q.jsx("div",{ref:x})]}),Y&&q.jsx("div",{className:"p-4 bg-white border-t border-gray-200",children:q.jsxs("div",{className:"flex items-center space-x-3",children:[q.jsx("audio",{controls:!0,src:Y,className:"flex-1"}),q.jsx("button",{onClick:J,className:"px-4 py-2 bg-whatsapp-green text-white rounded-lg hover:bg-whatsapp-green-light",children:"Send"}),q.jsx("button",{onClick:B,className:"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600",children:"Cancel"})]})}),q.jsxs("div",{className:"bg-white border-t border-gray-200 p-4",children:[q.jsxs("div",{className:"flex items-center space-x-3",children:[q.jsx("button",{className:"p-2 hover:bg-gray-100 rounded-full",children:q.jsx("svg",{className:"w-5 h-5 text-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),q.jsx("input",{type:"text",value:s,onChange:et=>o(et.target.value),onKeyPress:_t,placeholder:"Message...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:border-whatsapp-green"}),s.trim()?q.jsx("button",{onClick:P,className:"p-2 bg-whatsapp-green text-white rounded-full hover:bg-whatsapp-green-light",children:q.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})}):q.jsx("button",{onMouseDown:U,onMouseUp:L,onTouchStart:U,onTouchEnd:L,className:`p-2 rounded-full transition-colors ${C?"bg-red-500 text-white":"bg-whatsapp-green text-white hover:bg-whatsapp-green-light"}`,children:q.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{d:"M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"})})})]}),C&&q.jsx("div",{className:"mt-2 text-center",children:q.jsxs("span",{className:"text-red-500 text-sm",children:["Recording... ",D]})})]})]})};function mg(){return q.jsx(ev,{children:q.jsxs(w0,{children:[q.jsx(kc,{path:"/chat/:id",element:q.jsx(dg,{})}),q.jsx(kc,{path:"/",element:q.jsx(ov,{})})]})})}Lp.createRoot(document.getElementById("root")).render(q.jsx(O.StrictMode,{children:q.jsx(mg,{})}));
