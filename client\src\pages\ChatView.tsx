import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import MessageBubble from '../components/MessageBubble';
import useAudioRecorder from '../hooks/useAudioRecorder';
import useSocket from '../hooks/useSocket';
import useAuthStore from '../stores/authStore';

interface Message {
  _id: string;
  content?: string;
  messageType: 'text' | 'voice';
  audioFile?: {
    url: string;
    duration?: number;
  };
  createdAt: string;
  sender: {
    _id: string;
    username?: string;
  };
}

const ChatView: React.FC = () => {
  const { id: chatId } = useParams<{ id: string }>();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState<string>('');

  const [walkieTalkieMode, setWalkieTalkieMode] = useState<boolean>(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user, token } = useAuthStore();
  const socket = useSocket();
  
  const {
    isRecording,
    recordingDuration,
    audioUrl,
    startRecording,
    stopRecording,
    uploadAudio,
    resetRecording,
  } = useAudioRecorder();

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (socket && chatId) {
      socket.emit('join_chat', chatId);

      // Load existing messages
      loadMessages();

      // Listen for new messages
      socket.on('new_message', (message: Message) => {
        setMessages(prev => [...prev, message]);
      });

      // Listen for auto-play voice notes
      socket.on('auto_play_voice', (data: any) => {
        // This will be handled by the VoiceNotePlayer component
        console.log('Auto-play voice note:', data);
      });

      return () => {
        socket.off('new_message');
        socket.off('auto_play_voice');
      };
    }
  }, [socket, chatId]);

  const loadMessages = async (): Promise<void> => {
    try {
      const response = await fetch(`http://localhost:5000/api/chats/${chatId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const scrollToBottom = (): void => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const sendTextMessage = async (): Promise<void> => {
    if (!inputText.trim() || !socket) return;

    const messageData = {
      chatId,
      content: inputText,
      messageType: 'text' as const,
    };

    socket.emit('send_message', messageData);
    setInputText('');
  };

  const sendVoiceMessage = async (): Promise<void> => {
    if (!audioUrl || !socket) return;

    const audioFile = await uploadAudio();
    if (audioFile) {
      const messageData = {
        chatId,
        messageType: 'voice' as const,
        audioFile,
      };

      socket.emit('send_message', messageData);
      resetRecording();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendTextMessage();
    }
  };

  const toggleWalkieTalkie = (): void => {
    if (socket) {
      socket.emit('toggle_walkie_talkie', chatId);
      setWalkieTalkieMode(!walkieTalkieMode);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-chat-bg">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <button className="p-2 hover:bg-gray-100 rounded-full">
            <svg className="w-5 h-5 text-text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div className="w-10 h-10 bg-whatsapp-green rounded-full flex items-center justify-center">
            <span className="text-white font-medium">A</span>
          </div>
          <div>
            <h1 className="font-semibold text-text-primary">Chat</h1>
            <p className="text-sm text-text-secondary">Online</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Walkie-Talkie Toggle */}
          <button
            onClick={toggleWalkieTalkie}
            className={`p-2 rounded-full transition-colors ${
              walkieTalkieMode 
                ? 'bg-whatsapp-green text-white' 
                : 'hover:bg-gray-100 text-text-primary'
            }`}
            title="Toggle Walkie-Talkie Mode"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z" />
            </svg>
          </button>
          
          <button className="p-2 hover:bg-gray-100 rounded-full">
            <svg className="w-5 h-5 text-text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {messages.map((message) => (
          <MessageBubble
            key={message._id}
            message={message}
            isOwn={message.sender._id === user?.id}
            autoPlay={walkieTalkieMode && message.messageType === 'voice'}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Voice Recording Preview */}
      {audioUrl && (
        <div className="p-4 bg-white border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <audio controls src={audioUrl} className="flex-1" />
            <button
              onClick={sendVoiceMessage}
              className="px-4 py-2 bg-whatsapp-green text-white rounded-lg hover:bg-whatsapp-green-light"
            >
              Send
            </button>
            <button
              onClick={resetRecording}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-center space-x-3">
          <button className="p-2 hover:bg-gray-100 rounded-full">
            <svg className="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
          
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Message..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:border-whatsapp-green"
          />
          
          {inputText.trim() ? (
            <button
              onClick={sendTextMessage}
              className="p-2 bg-whatsapp-green text-white rounded-full hover:bg-whatsapp-green-light"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          ) : (
            <button
              onMouseDown={startRecording}
              onMouseUp={stopRecording}
              onTouchStart={startRecording}
              onTouchEnd={stopRecording}
              className={`p-2 rounded-full transition-colors ${
                isRecording 
                  ? 'bg-red-500 text-white' 
                  : 'bg-whatsapp-green text-white hover:bg-whatsapp-green-light'
              }`}
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z" />
              </svg>
            </button>
          )}
        </div>
        
        {isRecording && (
          <div className="mt-2 text-center">
            <span className="text-red-500 text-sm">Recording... {recordingDuration}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatView;
