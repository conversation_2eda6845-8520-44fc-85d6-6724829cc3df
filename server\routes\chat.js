import express from 'express';
import Chat from '../models/Chat.js';
import Message from '../models/Message.js';
import User from '../models/User.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Create new chat
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { isGroupChat, participants, name, participantEmail } = req.body;

    let participantIds = [];

    if (participantEmail) {
      // Find user by email for simple chat creation
      const otherUser = await User.findOne({ email: participantEmail });
      if (!otherUser) {
        return res.status(404).json({ message: 'User not found with this email' });
      }

      // Check if chat already exists between these users
      const existingChat = await Chat.findOne({
        isGroupChat: false,
        participants: { $all: [req.user._id, otherUser._id], $size: 2 }
      });

      if (existingChat) {
        return res.status(200).json(existingChat);
      }

      participantIds = [req.user._id, otherUser._id];
    } else if (participants) {
      participantIds = participants;
    } else {
      return res.status(400).json({ message: 'Either participantEmail or participants array is required' });
    }

    if (participantIds.length < 2) {
      return res.status(400).json({ message: 'At least two participants are required' });
    }

    // Create new chat
    const newChat = new Chat({
      isGroupChat: isGroupChat || false,
      participants: participantIds,
      name,
      groupAdmin: isGroupChat ? req.user._id : undefined
    });

    const chat = await newChat.save();

    // Populate the chat with participant details
    const populatedChat = await Chat.findById(chat._id)
      .populate('participants', '_id username email avatar');

    res.status(201).json(populatedChat);

  } catch (error) {
    console.error('Create chat error:', error);
    res.status(500).json({ message: 'Server error during chat creation' });
  }
});

// Get all chats for a user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const chats = await Chat.find({
      participants: { $in: [req.user._id] }
    }).populate('participants', '_id username email avatar')
      .populate('lastMessage');

    res.json(chats);

  } catch (error) {
    console.error('Get chats error:', error);
    res.status(500).json({ message: 'Server error fetching chats' });
  }
});

// Get messages for a specific chat
router.get('/:id/messages', authenticateToken, async (req, res) => {
  try {
    const { id: chatId } = req.params;
    
    const messages = await Message.find({ chat: chatId })
      .populate('sender', '_id username email avatar')
      .sort({ createdAt: 1 });

    res.json(messages);

  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ message: 'Server error fetching messages' });
  }
});

export default router;
