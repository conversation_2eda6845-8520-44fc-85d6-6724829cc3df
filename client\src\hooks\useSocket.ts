import { useEffect, useRef } from 'react';
import io from 'socket.io-client';
import useAuthStore from '../stores/authStore';

const useSocket = (): any => {
  const socketRef = useRef<any>(undefined);
  const { token, isAuthenticated } = useAuthStore();

  useEffect(() => {
    if (isAuthenticated && token) {
      // Initialize socket connection
      socketRef.current = io('http://localhost:5000', {
        transports: ['websocket'],
      });

      // Authenticate with server
      socketRef.current.emit('authenticate', token);

      // Handle authentication response
      socketRef.current.on('authenticated', (data: any) => {
        console.log('Socket authenticated:', data);
      });

      socketRef.current.on('authentication_error', (error: any) => {
        console.error('Socket authentication error:', error);
      });

      return () => {
        if (socketRef.current) {
          socketRef.current.disconnect();
        }
      };
    }
  }, [isAuthenticated, token]);

  return socketRef.current;
};

export default useSocket;
