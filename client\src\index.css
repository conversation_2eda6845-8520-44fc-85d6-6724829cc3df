@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #ffffff;
  color: #1f1f1f;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #cccccc;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999999;
}

/* Voice note waveform animation */
@keyframes waveform-pulse {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.5);
  }
}

.waveform-bar {
  animation: waveform-pulse 1s ease-in-out infinite;
}

.waveform-bar:nth-child(2) {
  animation-delay: 0.1s;
}

.waveform-bar:nth-child(3) {
  animation-delay: 0.2s;
}

.waveform-bar:nth-child(4) {
  animation-delay: 0.3s;
}

.waveform-bar:nth-child(5) {
  animation-delay: 0.4s;
}
