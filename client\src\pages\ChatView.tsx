import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import MessageBubble from '../components/MessageBubble';
import useAudioRecorder from '../hooks/useAudioRecorder';
import useSocket from '../hooks/useSocket';
import useAuthStore from '../stores/authStore';

interface Message {
  _id: string;
  content?: string;
  messageType: 'text' | 'voice';
  audioFile?: {
    url: string;
    duration?: number;
  };
  createdAt: string;
  sender: {
    _id: string;
    username?: string;
  };
}

const ChatView: React.FC = () => {
  const { id: chatId } = useParams<{ id: string }>();
  const [messages, setMessages] = useState<Message[]>([]);
  // Removed text input - this is voice-only

  const [walkieTalkieMode, setWalkieTalkieMode] = useState<boolean>(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user, token } = useAuthStore();
  const socket = useSocket();
  
  const {
    isRecording,
    recordingDuration,
    audioUrl,
    startRecording,
    stopRecording,
    uploadAudio,
    resetRecording,
  } = useAudioRecorder();

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (socket && chatId) {
      socket.emit('join_chat', chatId);

      // Load existing messages
      loadMessages();

      // Listen for new messages
      socket.on('new_message', (message: Message) => {
        setMessages(prev => [...prev, message]);
      });

      // Listen for auto-play voice notes
      socket.on('auto_play_voice', (data: any) => {
        // This will be handled by the VoiceNotePlayer component
        console.log('Auto-play voice note:', data);
      });

      return () => {
        socket.off('new_message');
        socket.off('auto_play_voice');
      };
    }
  }, [socket, chatId]);

  const loadMessages = async (): Promise<void> => {
    try {
      const response = await fetch(`http://localhost:5000/api/chats/${chatId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const scrollToBottom = (): void => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Removed text messaging - this is voice-only

  const sendVoiceMessage = useCallback(async (): Promise<void> => {
    if (!audioUrl || !socket) return;

    console.log('Uploading and sending voice message...');
    const audioFile = await uploadAudio();
    if (audioFile) {
      const messageData = {
        chatId,
        messageType: 'voice' as const,
        audioFile,
      };

      console.log('Sending voice message:', messageData);
      socket.emit('send_message', messageData);
      resetRecording();
    } else {
      console.error('Failed to upload audio file');
    }
  }, [audioUrl, socket, uploadAudio, chatId, resetRecording]);

  // Auto-send voice message when recording stops and audio is available
  useEffect(() => {
    if (audioUrl && !isRecording) {
      console.log('Auto-sending voice message...');
      sendVoiceMessage();
    }
  }, [audioUrl, isRecording, sendVoiceMessage]);

  // Removed text input handlers - voice-only

  const toggleWalkieTalkie = (): void => {
    if (socket) {
      socket.emit('toggle_walkie_talkie', chatId);
      setWalkieTalkieMode(!walkieTalkieMode);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-chat-bg">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <button className="p-2 hover:bg-gray-100 rounded-full">
            <svg className="w-5 h-5 text-text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div className="w-10 h-10 bg-whatsapp-green rounded-full flex items-center justify-center">
            <span className="text-white font-medium">A</span>
          </div>
          <div>
            <h1 className="font-semibold text-text-primary">Chat</h1>
            <p className="text-sm text-text-secondary">Online</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Walkie-Talkie Toggle */}
          <button
            onClick={toggleWalkieTalkie}
            className={`p-2 rounded-full transition-colors ${
              walkieTalkieMode 
                ? 'bg-whatsapp-green text-white' 
                : 'hover:bg-gray-100 text-text-primary'
            }`}
            title="Toggle Walkie-Talkie Mode"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z" />
            </svg>
          </button>
          
          <button className="p-2 hover:bg-gray-100 rounded-full">
            <svg className="w-5 h-5 text-text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {messages.map((message) => (
          <MessageBubble
            key={message._id}
            message={message}
            isOwn={message.sender._id === user?.id}
            autoPlay={walkieTalkieMode && message.messageType === 'voice'}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Voice Recording Preview */}
      {audioUrl && (
        <div className="p-4 bg-white border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <audio controls src={audioUrl} className="flex-1" />
            <button
              onClick={sendVoiceMessage}
              className="px-4 py-2 bg-whatsapp-green text-white rounded-lg hover:bg-whatsapp-green-light"
            >
              Send
            </button>
            <button
              onClick={resetRecording}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Voice Input Area - Voice Only */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-center justify-center space-x-4">
          {/* Voice Recording Button */}
          <button
            onMouseDown={startRecording}
            onMouseUp={stopRecording}
            onTouchStart={startRecording}
            onTouchEnd={stopRecording}
            className={`w-16 h-16 rounded-full transition-all duration-200 flex items-center justify-center ${
              isRecording
                ? 'bg-red-500 text-white scale-110 shadow-lg'
                : 'bg-whatsapp-green text-white hover:bg-whatsapp-green-light hover:scale-105 shadow-md'
            }`}
            disabled={!user}
          >
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z" />
            </svg>
          </button>
        </div>

        {isRecording && (
          <div className="mt-4 text-center">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-red-500 text-lg font-medium">Recording... {recordingDuration}</span>
            </div>
            <p className="text-text-secondary text-sm mt-1">Hold to record, release to send</p>
          </div>
        )}

        {!isRecording && !audioUrl && (
          <div className="mt-2 text-center">
            <p className="text-text-secondary text-sm">Hold the microphone to record a voice note</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatView;
