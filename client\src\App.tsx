import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useEffect } from 'react';
import ChatList from './pages/ChatList';
import ChatView from './pages/ChatView';
import Auth from './pages/Auth';
import useAuthStore from './stores/authStore';

function App(): React.JSX.Element {
  const { isAuthenticated, checkAuth, token } = useAuthStore();

  useEffect(() => {
    if (token) {
      checkAuth();
    }
  }, [token, checkAuth]);

  if (!isAuthenticated) {
    return <Auth />;
  }

  return (
    <Router>
      <Routes>
        <Route path="/chat/:id" element={<ChatView />} />
        <Route path="/" element={<ChatList />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
